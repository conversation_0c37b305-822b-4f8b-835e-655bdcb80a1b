FROM 10.30.200.236:5000/python:3.13-slim-bookworm-amd64

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY ./requirements.txt ./

# 复制应用代码
COPY ./backend/ ./backend/

# 创建必要的目录
RUN mkdir -p uploads generated_codes

RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org/debian-security|mirrors.ustc.edu.cn/debian-security|g' /etc/apt/sources.list.d/debian.sources && \
    apt update && \
    apt install dumb-init netcat-traditional curl iputils-ping -y && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean autoclean && \
    apt-get autoremove --yes && \
    pip config set global.index-url 'https://pypi.mirrors.ustc.edu.cn/simple' && \
    pip install --no-cache-dir -r requirements.txt && \
    rm -rf /var/lib/{apt,dpkg,cache,log}/

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "backend/run.py"]

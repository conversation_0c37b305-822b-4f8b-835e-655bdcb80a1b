# PLC Auto Coding System - 后端服务

基于FastAPI的PLC自动编码系统后端服务，提供数据库表维护和代码生成功能。

## 功能特性

- **数据库表管理**: 支持TP控制逻辑、OP控制逻辑、执行器变量映射三张表的CRUD操作
- **代码生成**: 集成现有的TP和OP代码生成逻辑，提供Web API接口
- **数据验证**: 使用Pydantic进行数据验证和序列化
- **API文档**: 自动生成OpenAPI/Swagger文档
- **错误处理**: 完善的异常处理机制
- **分页查询**: 支持分页、筛选、排序功能

## 技术栈

- **FastAPI**: 高性能Web框架
- **SQLAlchemy**: ORM数据库操作
- **Pydantic**: 数据验证和序列化
- **MySQL**: 数据库
- **Uvicorn**: ASGI服务器

## 项目结构

```
backend/
├── app/
│   ├── api/                # API路由
│   │   └── v1/            # API版本1
│   ├── core/              # 核心配置
│   ├── crud/              # 数据库操作
│   ├── models/            # 数据模型
│   ├── schemas/           # Pydantic模式
│   ├── services/          # 业务逻辑服务
│   └── main.py           # 主应用
├── run.py                # 启动脚本
└── README.md            # 说明文档
```

## 安装和运行

### 1. 安装依赖

```bash
# 在项目根目录下
pip install -e .
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，配置数据库连接等信息
```

### 3. 启动服务

```bash
# 方式1: 使用启动脚本
cd backend
python run.py

# 方式2: 直接使用uvicorn
uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 访问API文档

启动后访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API接口

### TP控制逻辑 (`/api/v1/tp-control`)

- `GET /` - 获取TP列表（支持分页和筛选）
- `GET /{id}` - 获取单个TP记录
- `POST /` - 创建TP记录
- `PUT /{id}` - 更新TP记录
- `DELETE /{id}` - 删除TP记录
- `GET /grouped/by-em` - 按EM描述分组获取

### OP控制逻辑 (`/api/v1/op-control`)

- `GET /` - 获取OP列表（支持分页和筛选）
- `GET /{id}` - 获取单个OP记录
- `POST /` - 创建OP记录
- `PUT /{id}` - 更新OP记录
- `DELETE /{id}` - 删除OP记录
- `GET /grouped/by-em` - 按EM描述分组获取

### 执行器变量映射 (`/api/v1/actuator-map`)

- `GET /` - 获取映射列表（支持分页和筛选）
- `GET /{id}` - 获取单个映射记录
- `POST /` - 创建映射记录
- `PUT /{id}` - 更新映射记录
- `DELETE /{id}` - 删除映射记录
- `GET /mapping/dict` - 获取映射字典

### 代码生成 (`/api/v1/code-generation`)

- `POST /tp` - 生成TP代码
- `POST /op` - 生成OP代码
- `GET /tp/download` - 下载TP代码文件
- `GET /op/download` - 下载OP代码文件
- `GET /validate` - 验证条件映射完整性

## 开发说明

### 添加新的API端点

1. 在`app/models/`中定义数据模型
2. 在`app/schemas/`中定义Pydantic模式
3. 在`app/crud/`中实现CRUD操作
4. 在`app/api/v1/`中创建路由
5. 在`app/api/v1/api.py`中注册路由

### 数据库迁移

当修改数据模型时，需要手动更新数据库表结构，或使用Alembic进行数据库迁移。

### 错误处理

系统提供了完善的错误处理机制，所有API响应都遵循统一的格式：

```json
{
  "success": true/false,
  "message": "响应消息",
  "data": "响应数据",
  "error_code": "错误代码（仅错误时）"
}
```

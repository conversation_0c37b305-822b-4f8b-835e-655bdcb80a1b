"""
应用配置模块
"""

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用设置"""

    # 应用基本信息
    app_name: str = "PLC Auto Coding System"
    app_version: str = "1.0.0"
    debug: bool = True

    # 数据库配置
    database_host: str = "*************"
    database_port: int = 3306
    database_user: str = "root"
    database_password: str = "leadchina"
    database_name: str = "IO_TEST"
    database_charset: str = "utf8mb4"

    # JWT配置
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # CORS配置
    allowed_origins: list = ["http://localhost:3000", "http://127.0.0.1:3000"]

    # 文件上传配置
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    upload_dir: str = "uploads"

    # 代码生成配置
    generated_code_dir: str = "generated_codes"

    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return (
            f"mysql+pymysql://{self.database_user}:{self.database_password}"
            f"@{self.database_host}:{self.database_port}/{self.database_name}"
            f"?charset={self.database_charset}"
        )

    class Config:
        env_file = ".env"
        case_sensitive = False


# 创建全局设置实例
settings = Settings()


def get_settings() -> Settings:
    """获取应用设置"""
    return settings

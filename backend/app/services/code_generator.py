"""
代码生成服务
使用重构后的TP和OP代码生成器
"""

import re
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy.orm import Session

from .tp_code_generator import TPCodeGeneratorService
from .op_code_generator import OPCodeGeneratorService
from ..crud.tp_control import tp_control
from ..crud.op_control import op_control
from ..crud.actuator_map import actuator_map


class WebCodeGeneratorService:
    """Web代码生成服务"""

    def __init__(self):
        self.tp_generator = None
        self.op_generator = None

    def _convert_tp_records_to_dict(self, records: List) -> List[Dict]:
        """将TP数据库记录转换为代码生成器需要的字典格式"""
        result = []
        for record in records:
            result.append(
                {
                    "id": record.id,
                    "step_id": record.step_id,
                    "step_description": record.step_description or "",
                    "motion_condition": record.motion_condition or "",
                    "motion_action": record.motion_action or "",
                    "skip_condition": record.skip_condition or "",
                    "skip_step": record.skip_step or "",
                    "tp_sequence": record.tp_sequence or "",
                    "em_description_cn": record.em_description_cn or "",
                    "em_variable": record.em_variable or "",
                    "op_sequence_under_em": record.op_sequence_under_em or "",
                }
            )
        return result

    def _convert_op_records_to_dict(self, records: List) -> List[Dict]:
        """将OP数据库记录转换为代码生成器需要的字典格式"""
        result = []
        for record in records:
            result.append(
                {
                    "id": record.id,
                    "step_id": record.step_id,
                    "step_description": record.step_description or "",
                    "watchdog_mask": record.watchdog_mask or "",
                    "action_description": record.action_description or "",
                    "normal_interlock_condition": record.normal_interlock_condition
                    or "",
                    "recovery_interlock_condition": record.recovery_interlock_condition
                    or "",
                    "alarm_condition": record.alarm_condition or "",
                    "skip_condition": record.skip_condition or "",
                    "em_description_cn": record.em_description_cn or "",
                    "op_sequence_under_em": record.op_sequence_under_em or "",
                    "em_variable": record.em_variable or "",
                    "recovery_interlock_action": record.recovery_interlock_action or "",
                }
            )
        return result

    def _convert_actuator_records_to_dict(self, records: List) -> List[Dict]:
        """将执行器映射记录转换为代码生成器需要的字典格式"""
        result = []
        for record in records:
            result.append(
                {
                    "parent_struct": record.parent_struct or "",
                    "actuator": record.actuator or "",
                    "command_variable": record.command_variable or "",
                    "command_description_cn": record.command_description_cn or "",
                }
            )
        return result

    def find_condition_mapping(
        self, db: Session, condition_text: str, step_data: Dict
    ) -> Optional[str]:
        """使用整合的搜索功能查找条件映射"""
        return actuator_map.find_matching_condition(db, condition_text, step_data)

    def search_unmapped_conditions(
        self, db: Session, conditions: List[str]
    ) -> Dict[str, List[str]]:
        """搜索未映射的条件并提供建议"""
        result: Dict[str, List[str]] = {"unmapped": [], "suggestions": []}

        for condition in conditions:
            # 尝试查找映射
            step_data = {
                "em_variable": "io_stEmBtryIn",
                "op_sequence_under_em": "OP[1]",
            }
            mapping = self.find_condition_mapping(db, condition, step_data)

            if not mapping:
                result["unmapped"].append(condition)

                # 搜索相似的映射作为建议
                similar_mappings = actuator_map.search_by_condition_pattern(
                    db, condition
                )
                if similar_mappings:
                    suggestions = []
                    for mapping_record in similar_mappings[:3]:  # 最多3个建议
                        suggestions.append(
                            {
                                "description": mapping_record.command_description_cn,
                                "parent_struct": mapping_record.parent_struct,
                                "actuator": mapping_record.actuator,
                                "command_variable": mapping_record.command_variable,
                            }
                        )
                    result["suggestions"].append(
                        str({"condition": condition, "similar_mappings": suggestions})
                    )

        return result

    def generate_tp_code(
        self, db: Session, em_description: Optional[str] = None
    ) -> Tuple[str, List[str], List[str]]:
        """
        生成TP代码

        Args:
            db: 数据库会话
            em_description: EM描述筛选条件

        Returns:
            Tuple[生成的代码, 警告信息, 未映射的条件]
        """
        # 获取TP数据
        if em_description:
            tp_records = tp_control.get_by_em_description(
                db, em_description=em_description
            )
        else:
            tp_records = tp_control.get_multi(db, limit=1000)

        # 获取执行器映射数据
        actuator_records = actuator_map.get_multi(db, limit=1000)

        if not tp_records:
            return "", ["没有找到TP数据"], []

        # 创建代码生成器实例
        generator = TPCodeGeneratorService()

        # 转换数据格式
        step_data = self._convert_tp_records_to_dict(tp_records)
        command_data = self._convert_actuator_records_to_dict(actuator_records)

        # 生成代码
        try:
            generated_code = generator.generate_all_tp_code(step_data, command_data)
            warnings = []
            unmapped_conditions = list(generator.unmapped_conditions)

            # 如果有未映射条件，添加映射模板到警告中
            if unmapped_conditions:
                template = generator.get_condition_mapping_template()
                warnings.append("存在未映射的条件，请参考以下SQL模板添加映射：")
                warnings.append(template)

            return generated_code, warnings, unmapped_conditions

        except Exception as e:
            return "", [f"代码生成失败: {str(e)}"], []

    def generate_op_code(
        self, db: Session, em_description: Optional[str] = None
    ) -> Tuple[str, List[str], List[str]]:
        """
        生成OP代码

        Args:
            db: 数据库会话
            em_description: EM描述筛选条件

        Returns:
            Tuple[生成的代码, 警告信息, 未映射的条件]
        """
        # 获取OP数据
        if em_description:
            op_records = op_control.get_by_em_description(
                db, em_description=em_description
            )
        else:
            op_records = op_control.get_multi(db, limit=1000)

        # 获取执行器映射数据
        actuator_records = actuator_map.get_multi(db, limit=1000)

        if not op_records:
            return "", ["没有找到OP数据"], []

        # 创建代码生成器实例
        generator = OPCodeGeneratorService()

        # 转换数据格式
        step_data = self._convert_op_records_to_dict(op_records)
        actuator_data = self._convert_actuator_records_to_dict(actuator_records)

        # 生成代码
        try:
            generated_code = generator.generate_all_op_code(step_data, actuator_data)
            warnings = []
            unmapped_conditions = list(generator.unmapped_conditions)

            # 如果有未映射条件，添加映射模板到警告中
            if unmapped_conditions:
                template = generator.get_condition_mapping_template()
                warnings.append("存在未映射的条件，请参考以下SQL模板添加映射：")
                warnings.append(template)

            return generated_code, warnings, unmapped_conditions

        except Exception as e:
            return "", [f"代码生成失败: {str(e)}"], []

    def validate_conditions(self, db: Session) -> Dict[str, List[str]]:
        """
        验证条件映射完整性

        Returns:
            Dict包含各种验证结果
        """
        result: Dict[str, List[str]] = {
            "tp_unmapped": [],
            "op_unmapped": [],
            "suggestions": [],
        }

        try:
            # 验证TP条件映射
            _, _, tp_unmapped = self.generate_tp_code(db)
            result["tp_unmapped"] = tp_unmapped

            # 验证OP条件映射
            _, _, op_unmapped = self.generate_op_code(db)
            result["op_unmapped"] = op_unmapped

            # 使用新的搜索功能生成映射建议
            all_unmapped = list(set(tp_unmapped + op_unmapped))
            if all_unmapped:
                suggestions_result = self.search_unmapped_conditions(db, all_unmapped)
                result["suggestions"] = suggestions_result.get("suggestions", [])

        except Exception as e:
            result["error"] = [str(e)]

        return result

    def get_condition_mapping_suggestions(
        self, db: Session, condition_text: str
    ) -> Dict[str, Any]:
        """获取条件映射建议"""
        # 搜索相似的映射
        similar_mappings = actuator_map.search_by_condition_pattern(db, condition_text)

        # 提取执行器名称（如果有）
        extracted_actuator = actuator_map.extract_actuator_name(condition_text)

        # 提取条件名称部分
        condition_part = actuator_map.extract_condition_name(condition_text)

        return {
            "original_condition": condition_text,
            "extracted_actuator": extracted_actuator,
            "condition_part": condition_part,
            "similar_mappings": [
                {
                    "id": m.id,
                    "description": m.command_description_cn,
                    "parent_struct": m.parent_struct,
                    "actuator": m.actuator,
                    "command_variable": m.command_variable,
                }
                for m in similar_mappings
            ],
            "suggestion_template": {
                "parent_struct": "Y",  # 建议使用动态EM变量
                "actuator": "Y" if extracted_actuator != condition_text else "N",
                "command_variable": "Ctrl.请修改此处",
                "command_description_cn": condition_text,
            },
        }

    def analyze_conditions_detailed(self, db: Session) -> Dict[str, Any]:
        """详细分析条件映射情况"""
        analysis: Dict[str, Any] = {
            "logical_operators_found": [],
            "regex_patterns_found": [],
            "actuator_patterns_found": [],
            "chinese_conditions_found": [],
            "mapping_coverage": {},
            "condition_types": {},
        }

        try:
            # 获取所有TP和OP数据
            tp_records = tp_control.get_multi(db, limit=1000)
            op_records = op_control.get_multi(db, limit=1000)

            all_conditions = []

            # 收集TP条件
            for record in tp_records:
                conditions = [record.motion_condition, record.skip_condition]
                for cond in conditions:
                    if cond and cond.strip():
                        all_conditions.append(cond.strip())

            # 收集OP条件
            for record in op_records:
                conditions = [
                    record.normal_interlock_condition,
                    record.recovery_interlock_condition,
                    record.alarm_condition,
                    record.skip_condition,
                ]
                for cond in conditions:
                    if cond and cond.strip():
                        all_conditions.append(cond.strip())

            # 分析条件类型
            for condition in set(all_conditions):
                # 检查逻辑运算符
                if any(
                    op in condition
                    for op in ["AND", "OR", "NOT", "_AND_", "_OR_", "_NOT_"]
                ):
                    analysis["logical_operators_found"].append(condition)

                # 检查正则模式
                if re.search(r"在位置\d+", condition):
                    analysis["regex_patterns_found"].append(condition)

                # 检查执行器模式
                if re.search(r"[A-Za-z]+\d+", condition):
                    analysis["actuator_patterns_found"].append(condition)

                # 检查中文条件
                if re.search(r"[\u4e00-\u9fff]+", condition):
                    analysis["chinese_conditions_found"].append(condition)

                # 分析条件类型
                if "AND" in condition or "OR" in condition:
                    analysis["condition_types"][condition] = "complex_logical"
                elif re.search(r"[A-Za-z]+\d+", condition):
                    analysis["condition_types"][condition] = "actuator_based"
                elif re.search(r"[\u4e00-\u9fff]+", condition):
                    analysis["condition_types"][condition] = "chinese_description"
                else:
                    analysis["condition_types"][condition] = "simple"

            # 计算映射覆盖率
            total_conditions = len(set(all_conditions))
            mapped_conditions = 0

            for condition in set(all_conditions):
                step_data = {
                    "em_variable": "io_stEmBtryIn",
                    "op_sequence_under_em": "OP[1]",
                }
                mapping = self.find_condition_mapping(db, condition, step_data)
                if mapping and not mapping.startswith("/* 未映射"):
                    mapped_conditions += 1

            analysis["mapping_coverage"] = {
                "total_conditions": total_conditions,
                "mapped_conditions": mapped_conditions,
                "unmapped_conditions": total_conditions - mapped_conditions,
                "coverage_percentage": round(
                    (mapped_conditions / total_conditions * 100)
                    if total_conditions > 0
                    else 0,
                    2,
                ),
            }

        except Exception as e:
            analysis["error"] = str(e)

        return analysis


# 创建全局服务实例
code_generator_service = WebCodeGeneratorService()

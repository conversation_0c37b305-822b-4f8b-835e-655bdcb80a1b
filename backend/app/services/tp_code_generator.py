"""
TP代码生成器服务
严格按照历史版本逻辑生成PLC代码
"""

from typing import Dict, List, Optional, Set
import re


class TPCodeGeneratorService:
    """TP代码生成器服务类"""

    def __init__(self):
        self.unmapped_conditions: Set[str] = set()
        self.command_data: List[Dict] = []

    def safe_get_string(self, data: Dict, key: str, default: str = "") -> str:
        """安全获取字符串值"""
        value = data.get(key, default)
        return str(value) if value is not None else default

    def safe_get_int(self, data: Dict, key: str, default: int = 0) -> int:
        """安全获取整数值"""
        try:
            value = data.get(key, default)
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            return default

    def build_condition_mappings(self) -> Dict[str, Dict[str, str]]:
        """构建条件映射字典（从command_data）"""
        mappings = {}

        for command in self.command_data:
            description = self.safe_get_string(command, "command_description_cn")
            if description:
                mappings[description] = {
                    "parent_struct": self.safe_get_string(command, "parent_struct"),
                    "actuator": self.safe_get_string(command, "actuator"),
                    "command_variable": self.safe_get_string(
                        command, "command_variable"
                    ),
                }

        return mappings

    def _get_basic_condition_mappings(
        self, step: Optional[Dict] = None
    ) -> Dict[str, str]:
        """从数据库获取基本条件映射"""
        if not step:
            return {}

        em_variable = step.get("em_variable", "io_stEmBtryIn")
        op_sequence = step.get("op_sequence_under_em", "OP[1]")

        # 从数据库构建条件映射
        condition_mappings = self.build_condition_mappings()
        basic_mappings = {}

        for description, mapping_data in condition_mappings.items():
            parent_struct = self.safe_get_string(mapping_data, "parent_struct")
            actuator_type = self.safe_get_string(mapping_data, "actuator")
            command_var = self.safe_get_string(mapping_data, "command_variable")

            if actuator_type == "Y":
                # 动态拼接EM变量
                if parent_struct == "Y":
                    basic_mappings[description] = f"{em_variable}.{command_var}"
                else:
                    basic_mappings[description] = f"{parent_struct}.{command_var}"
            elif actuator_type == "N":
                # 不拼接执行器
                if parent_struct == "Y":
                    basic_mappings[description] = f"{em_variable}.{command_var}"
                else:
                    basic_mappings[description] = f"{command_var}"
            elif actuator_type == "OP":
                # 拼接OP序列
                if parent_struct == "Y":
                    basic_mappings[description] = (
                        f"{em_variable}.{op_sequence}.{command_var}"
                    )
                else:
                    basic_mappings[description] = (
                        f"{parent_struct}.{op_sequence}.{command_var}"
                    )

        # 处理bDoneAction[N]的动态拼接，支持1-99
        for i in range(1, 100):  # 支持bDoneAction[1]到bDoneAction[99]
            basic_mappings[f"bDoneAction[{i}]"] = (
                f"{em_variable}.{op_sequence}.bDoneAction[{i}]"
            )

        # 同样处理其他可能的动作数组
        for i in range(1, 100):
            basic_mappings[f"bCmdAction[{i}]"] = (
                f"{em_variable}.{op_sequence}.bCmdAction[{i}]"
            )
            basic_mappings[f"bConAction[{i}]"] = (
                f"{em_variable}.{op_sequence}.bConAction[{i}]"
            )
            basic_mappings[f"bSkipAction[{i}]"] = (
                f"{em_variable}.{op_sequence}.bSkipAction[{i}]"
            )
            basic_mappings[f"bAlm[{i}]"] = f"{em_variable}.{op_sequence}.bAlm[{i}]"

        return basic_mappings

    def _is_plc_variable(self, text: str) -> bool:
        """检查是否是完整的PLC变量格式（包含点号的完整路径）"""
        # 完整PLC变量模式：必须包含点号，表示完整路径
        pattern = r"^[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z0-9_.\[\]]+$"
        return bool(re.match(pattern, text))

    def extract_actuator_name(self, text: str) -> str:
        """从文本中提取执行器名称（如SV1、YV2等）"""
        # 匹配字母+数字的组合
        match = re.search(r"([A-Za-z]+)(\d+)", text)
        if match:
            letters = match.group(1)
            numbers = match.group(2)
            return f"{letters}[{numbers}]"
        return text

    def _parse_complex_condition_new(
        self, condition_text: str, step: Optional[Dict] = None
    ) -> str:
        """解析新格式的复合条件（用下划线分隔）"""
        basic_mappings = self._get_basic_condition_mappings(step)

        # 直接使用智能解析，不管是否有括号
        return self._parse_complex_condition_with_parentheses(
            condition_text, basic_mappings, step
        )

    def _parse_complex_condition_with_parentheses(
        self,
        condition_text: str,
        basic_mappings: Dict[str, str],
        step: Optional[Dict] = None,
    ) -> str:
        """处理包含括号的复杂条件"""
        result = condition_text

        # 第一步：处理bDoneAction[N]和bCmdAction[N]等模式
        action_pattern = (
            r"\b(bDoneAction|bCmdAction|bConAction|bSkipAction|bAlm)\[(\d+)\]"
        )

        def replace_action(match):
            action_type = match.group(1)
            number = match.group(2)
            original = f"{action_type}[{number}]"

            # 检查这个匹配是否已经是完整PLC变量的一部分
            # 获取匹配在原字符串中的位置
            start_pos = match.start()

            # 检查匹配前是否有点号，如果有，说明这已经是完整PLC变量的一部分
            if start_pos > 0 and result[start_pos - 1] == ".":
                # 进一步检查是否是完整的PLC变量路径
                # 向前查找，看是否有完整的变量路径
                prefix = result[:start_pos]
                # 如果前缀包含io_st开头的变量，说明这是完整的PLC变量
                if re.search(r"io_st[a-zA-Z0-9_]*(\.[a-zA-Z0-9_\[\]]+)*\.$", prefix):
                    return original  # 保持原样，不进行映射

            if original in basic_mappings:
                return basic_mappings[original]
            return original

        result = re.sub(action_pattern, replace_action, result)

        # 第二步：按优先级替换其他映射（长的先替换，避免部分匹配）
        sorted_mappings = sorted(
            basic_mappings.items(), key=lambda x: len(x[0]), reverse=True
        )

        for description, mapping in sorted_mappings:
            # 跳过已经处理的动作模式
            if re.match(r".*\[\d+\]$", description):
                continue

            # 使用完整匹配
            if description in result:
                result = result.replace(description, mapping)

        # 第三步：检查是否还有未映射的内容
        # 查找所有已知的PLC变量（包含方括号的完整变量）
        plc_var_pattern = (
            r"\b[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z0-9_]+)*(\[[a-zA-Z0-9_]+\])*"
        )

        # 创建一个临时结果用于检查未映射条件
        temp_result = result

        # 移除所有已知的PLC变量
        temp_result = re.sub(plc_var_pattern, " ", temp_result)

        # 移除逻辑运算符和括号
        temp_result = re.sub(r"\b(AND|OR|NOT)\b", " ", temp_result)
        temp_result = re.sub(r"[()[\]]", " ", temp_result)  # 移除所有括号

        # 剩下的非空白内容就是未映射的条件
        remaining_tokens = [
            token.strip() for token in temp_result.split() if token.strip()
        ]

        for token in remaining_tokens:
            # 过滤掉单个字符和数字
            if token and len(token) > 1 and not token.isdigit():
                self.unmapped_conditions.add(token)

        return result

    def _advanced_parse_condition(
        self, condition_text: str, mappings: Dict[str, Dict[str, str]], step: Dict
    ) -> str:
        """高级条件解析 - 处理更复杂的逻辑表达式"""
        # 处理 NOT（优先级最高）
        if condition_text.startswith("NOT "):
            base_condition = condition_text[4:].strip()
            mapped_base = self._advanced_parse_condition(base_condition, mappings, step)
            return f"NOT {mapped_base}"

        # 处理 OR（优先级最低）
        if " OR " in condition_text:
            or_parts = condition_text.split(" OR ")
            mapped_parts = []
            for part in or_parts:
                part = part.strip()
                mapped_part = self._advanced_parse_condition(part, mappings, step)
                mapped_parts.append(mapped_part)
            return " OR ".join(mapped_parts)

        # 处理 AND（优先级中等）
        if " AND " in condition_text:
            and_parts = condition_text.split(" AND ")
            mapped_parts = []
            for part in and_parts:
                part = part.strip()
                mapped_part = self._advanced_parse_condition(part, mappings, step)
                mapped_parts.append(mapped_part)
            return " AND ".join(mapped_parts)

        # 处理单个条件
        return self._map_single_condition(condition_text, mappings, step)

    def _map_single_condition(
        self, condition_text: str, mappings: Dict[str, Dict[str, str]], step: Dict
    ) -> str:
        """映射单个条件"""
        # 直接匹配
        if condition_text in mappings:
            mapping_data = mappings[condition_text]
            return self._build_variable_path(mapping_data, step, condition_text)

        # 提取执行器和条件部分进行匹配
        actuator_match = re.search(r"([A-Za-z]+\d+)(.+)", condition_text)
        if actuator_match:
            actuator_part = actuator_match.group(1)  # 如 "SV1"
            condition_part = actuator_match.group(2).strip()  # 如 "在零点"

            # 在映射表中查找条件部分
            for desc, mapping_data in mappings.items():
                if condition_part in desc:
                    actuator_type = self.safe_get_string(mapping_data, "actuator")
                    if actuator_type == "Y":
                        # 替换执行器名称
                        formatted_actuator = self.extract_actuator_name(actuator_part)
                        return self._replace_actuator_in_path(
                            mapping_data, formatted_actuator, step
                        )

        # 如果找不到匹配，记录未映射条件并返回原文
        self.unmapped_conditions.add(condition_text)
        return condition_text

    def _build_variable_path(
        self, mapping_data: Dict[str, str], step: Dict, condition_text: str
    ) -> str:
        """构建变量路径"""
        parent_struct = self.safe_get_string(mapping_data, "parent_struct")
        actuator_type = self.safe_get_string(mapping_data, "actuator")
        command_var = self.safe_get_string(mapping_data, "command_variable")

        em_variable = step.get("em_variable", "io_stEmBtryIn")
        op_sequence = step.get("op_sequence_under_em", "OP[1]")

        if actuator_type == "Y":
            if parent_struct == "Y":
                return f"{em_variable}.{command_var}"
            else:
                return f"{parent_struct}.{command_var}"
        elif actuator_type == "N":
            if parent_struct == "Y":
                return f"{em_variable}.{command_var}"
            else:
                return f"{command_var}"
        elif actuator_type == "OP":
            if parent_struct == "Y":
                return f"{em_variable}.{op_sequence}.{command_var}"
            else:
                return f"{parent_struct}.{op_sequence}.{command_var}"

        return condition_text

    def _replace_actuator_in_path(
        self, mapping_data: Dict[str, str], formatted_actuator: str, step: Dict
    ) -> str:
        """在变量路径中替换执行器名称"""
        parent_struct = self.safe_get_string(mapping_data, "parent_struct")
        command_var = self.safe_get_string(mapping_data, "command_variable")

        em_variable = step.get("em_variable", "io_stEmBtryIn")

        if parent_struct == "Y":
            return f"{em_variable}.{formatted_actuator}.{command_var}"
        else:
            return f"{parent_struct}.{formatted_actuator}.{command_var}"

    def generate_condition_mapping(
        self, condition_text: str, step: Optional[Dict] = None
    ) -> str:
        """生成条件映射（严格按照历史版本逻辑）"""
        if not condition_text or condition_text.strip() == "":
            return "FALSE"

        condition_text = condition_text.strip()

        if condition_text == "TRUE":
            return "TRUE"
        elif condition_text == "FALSE":
            return "FALSE"
        else:
            # 检查是否是PLC变量格式（字母、下划线、方括号、数字的组合）
            if self._is_plc_variable(condition_text):
                return condition_text

            # 添加一些基本的条件映射
            basic_mappings = self._get_basic_condition_mappings(step)

            # 检查基本映射
            if condition_text in basic_mappings:
                return basic_mappings[condition_text]

            # 处理复合条件（包含AND、OR、NOT或括号的条件）
            if (
                "AND" in condition_text
                or "OR" in condition_text
                or "NOT" in condition_text
                or "(" in condition_text
                or ")" in condition_text
                or "_AND_" in condition_text
                or "_OR_" in condition_text
                or condition_text.startswith("NOT_")
            ):
                return self._parse_complex_condition_new(condition_text, step)

            # 从数据库命令表构建条件映射
            condition_mappings = self.build_condition_mappings()

            # 使用高级解析方法处理条件
            result = self._advanced_parse_condition(
                condition_text, condition_mappings, step or {}
            )

            return result

    def extract_tp_number(self, tp_sequence: str) -> str:
        """提取TP序列号"""
        tp_num_match = re.search(r"TP\[(\d+)\]", tp_sequence)
        return tp_num_match.group(1) if tp_num_match else "1"

    def _find_next_step(self, steps: List[Dict], current_index: int) -> str:
        """查找下一个步骤ID"""
        if current_index + 1 < len(steps):
            return str(self.safe_get_int(steps[current_index + 1], "step_id"))
        return "500"  # 默认结束步骤

    def group_steps_by_tp(self, step_data: List[Dict]) -> Dict[str, List[Dict]]:
        """按TP序列分组步骤"""
        tp_groups: Dict[str, List[Dict]] = {}

        for step in step_data:
            tp_sequence = self.safe_get_string(step, "tp_sequence", "io_stUm.TP[1]")
            if tp_sequence not in tp_groups:
                tp_groups[tp_sequence] = []
            tp_groups[tp_sequence].append(step)

        # 为每个组按step_id排序
        for tp_sequence in tp_groups:
            tp_groups[tp_sequence].sort(key=lambda x: self.safe_get_int(x, "step_id"))

        return tp_groups

    def generate_tp_process_code(self, tp_sequence: str, steps: List[Dict]) -> str:
        """生成单个TP流程代码（严格按照历史版本逻辑）"""
        if not steps:
            return ""

        tp_num = self.extract_tp_number(tp_sequence)
        em_variable = self.safe_get_string(steps[0], "em_variable", "io_stEmBtryIn")
        op_sequence = self.safe_get_string(steps[0], "op_sequence_under_em", "OP[1]")
        em_description = self.safe_get_string(steps[0], "em_description_cn", "")

        code_lines = []
        code_lines.append(
            f"//====================================TP{tp_num}{em_description}自动流程===================================="
        )
        code_lines.append("IF io_stUm.CmdSta.iCmdNo=2 THEN")
        code_lines.append(f"\tCASE io_stUm.TP[{tp_num}].iCmdStep OF")

        # 按step_id排序
        steps = sorted(steps, key=lambda x: self.safe_get_int(x, "step_id"))

        for i, step in enumerate(steps):
            step_id = self.safe_get_int(step, "step_id")
            step_desc = self.safe_get_string(step, "step_description", f"步骤{step_id}")

            if step_id == 0:
                # 等待启动信号的特殊处理
                code_lines.append(
                    f"\t\t{step_id}:\tio_stUm.TP[{tp_num}].strStepInfo:='{step_desc}';"
                )
                code_lines.append("\t\t\tIF io_stUm.CmdSta.bCmdRun THEN\t")

                # 找到下一个步骤
                next_step = self._find_next_step(steps, i)
                if next_step:
                    code_lines.append(
                        f"\t\t\t\tio_stUm.TP[{tp_num}].iCmdStep:={next_step};"
                    )
                code_lines.append("\t\t\tEND_IF;")

            elif step_id == 500:
                # 结束步骤的特殊处理
                code_lines.append(
                    f"\t\t{step_id}:io_stUm.TP[{tp_num}].strStepInfo:='{step_desc}';"
                )
                code_lines.append(f"\t\t\tio_stUm.TP[{tp_num}].bCmdDone:=TRUE;")
                code_lines.append(f"\t\t\tio_stUm.TP[{tp_num}].iCmdStep:=0;")

            else:
                # 普通步骤处理
                code_lines.append(f"\t\t{step_id}://{step_desc}")
                code_lines.append(
                    f"\t\t\tio_stUm.TP[{tp_num}].strStepInfo:={em_variable}.{op_sequence}.strStepInfo;"
                )

                # 检查是否有运动逻辑（motion_condition和motion_action都不为空）
                motion_condition = step.get("motion_condition", "")
                motion_action = step.get("motion_action", "")

                if (
                    motion_condition
                    and motion_condition.strip()
                    and motion_action
                    and motion_action.strip()
                ):
                    # 添加运动条件和动作
                    self._add_motion_logic_new(
                        code_lines, step, em_variable, op_sequence, tp_num
                    )

                # 添加跳步逻辑
                self._add_skip_logic_new(code_lines, step, tp_num)

            code_lines.append("")

        code_lines.append("\tEND_CASE;")
        code_lines.append("END_IF;")
        code_lines.append("")

        return "\n".join(code_lines)

    def _add_motion_logic_new(
        self,
        code_lines: List[str],
        step: Dict,
        em_variable: str,
        op_sequence: str,
        tp_num: str,
    ):
        """添加新的运动逻辑（严格按照历史版本格式）"""
        motion_condition = step.get("motion_condition", "TRUE")
        motion_action = step.get("motion_action", "")

        code_lines.append("\t\t    IF io_stUm.CmdSta.bCmdRun THEN")

        # 解析运动条件
        parsed_condition = self.generate_condition_mapping(motion_condition, step)
        code_lines.append(f"\t\t\t\tIF  {parsed_condition} THEN")

        # 添加运动动作
        if motion_action and motion_action.strip():
            code_lines.append(
                f"\t\t\t\t\t {em_variable}.{op_sequence}.{motion_action}:=TRUE;"
            )

        code_lines.append("\t\t\t\tEND_IF;")
        code_lines.append("\t\t\tEND_IF;")
        code_lines.append("")

    def _add_skip_logic_new(self, code_lines: List[str], step: Dict, tp_num: str):
        """添加新的跳步逻辑（严格按照历史版本格式）"""
        skip_condition = step.get("skip_condition", "")
        skip_step = step.get("skip_step", "")

        if (
            not skip_condition
            or skip_condition.strip() == ""
            or skip_condition == "FALSE"
        ):
            return

        # 解析跳步条件和目标步骤（支持分号分隔的多条件多目标）
        conditions = [c.strip() for c in skip_condition.split(";") if c.strip()]
        steps_list = [s.strip() for s in skip_step.split(";") if s.strip()]

        if not conditions:
            return

        # 生成IF-ELSIF结构
        for i, condition in enumerate(conditions):
            parsed_condition = self.generate_condition_mapping(condition, step)
            target_step = (
                steps_list[i] if i < len(steps_list) else steps_list[-1]
            )  # 如果目标步骤不够，使用最后一个

            if i == 0:
                code_lines.append(f"\t\t\tIF {parsed_condition} THEN")
            else:
                code_lines.append(f"\t\t\tELSIF {parsed_condition} THEN")

            code_lines.append(f"\t\t\t\tio_stUm.TP[{tp_num}].iCmdStep:={target_step};")

        code_lines.append("\t\t\tEND_IF;")

    def generate_all_tp_code(
        self, step_data: List[Dict], command_data: List[Dict]
    ) -> str:
        """生成所有PLC代码"""
        # 清空未映射条件记录
        self.unmapped_conditions.clear()
        self.command_data = command_data

        tp_groups = self.group_steps_by_tp(step_data)
        all_code = []

        # 生成每个TP流程的代码
        for tp_sequence, steps in tp_groups.items():
            process_code = self.generate_tp_process_code(tp_sequence, steps)
            all_code.append(process_code)
            all_code.append("")

        # 如果有未映射条件，在代码末尾添加提示
        if self.unmapped_conditions:
            all_code.append("\n" + "/" * 80)
            all_code.append("// ⚠️  代码生成警告：存在未映射的条件")
            all_code.append("// 请检查上方的警告信息并完善条件映射表")
            all_code.append("/" * 80)

        return "\n".join(all_code)

    def get_condition_mapping_template(self) -> str:
        """生成条件映射模板SQL"""
        if not self.unmapped_conditions:
            return "-- 所有条件都已映射，无需添加新的映射"

        template_lines = []
        template_lines.append("-- 未映射条件的SQL插入模板")
        template_lines.append(
            "-- 请根据实际情况修改parent_struct, actuator, command_variable字段"
        )
        template_lines.append("")

        for i, condition in enumerate(sorted(self.unmapped_conditions), 1):
            template_lines.append(
                "INSERT INTO actuator_variable_map (parent_struct, actuator, command_variable, command_description_cn) VALUES"
            )
            template_lines.append(f"('Y', 'Y', 'Ctrl.请修改此处', '{condition}');")
            template_lines.append("")

        return "\n".join(template_lines)

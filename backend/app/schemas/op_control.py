"""
OP控制逻辑的Pydantic模式
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class OPControlLogicBase(BaseModel):
    """OP控制逻辑基础模式"""

    step_id: str = Field(..., description="步骤ID")
    step_description: Optional[str] = Field(None, description="步骤描述")
    watchdog_mask: Optional[str] = Field(None, description="看门狗掩码")
    action_description: Optional[str] = Field(None, description="动作描述")
    normal_interlock_condition: Optional[str] = Field(None, description="正常互锁条件")
    recovery_interlock_condition: Optional[str] = Field(
        None, description="恢复互锁条件"
    )
    alarm_condition: Optional[str] = Field(None, description="报警条件")
    skip_condition: Optional[str] = Field(None, description="跳步条件")
    em_description_cn: Optional[str] = Field(None, description="EM描述(中文)")
    op_sequence_under_em: Optional[str] = Field(None, description="EM下的OP序列")
    em_variable: Optional[str] = Field(None, description="EM变量")
    recovery_interlock_action: Optional[str] = Field(None, description="恢复互锁动作")


class OPControlLogicCreate(OPControlLogicBase):
    """创建OP控制逻辑的模式"""

    pass


class OPControlLogicUpdate(BaseModel):
    """更新OP控制逻辑的模式"""

    step_id: Optional[str] = Field(None, description="步骤ID")
    step_description: Optional[str] = Field(None, description="步骤描述")
    watchdog_mask: Optional[str] = Field(None, description="看门狗掩码")
    action_description: Optional[str] = Field(None, description="动作描述")
    normal_interlock_condition: Optional[str] = Field(None, description="正常互锁条件")
    recovery_interlock_condition: Optional[str] = Field(
        None, description="恢复互锁条件"
    )
    alarm_condition: Optional[str] = Field(None, description="报警条件")
    skip_condition: Optional[str] = Field(None, description="跳步条件")
    em_description_cn: Optional[str] = Field(None, description="EM描述(中文)")
    op_sequence_under_em: Optional[str] = Field(None, description="EM下的OP序列")
    em_variable: Optional[str] = Field(None, description="EM变量")
    recovery_interlock_action: Optional[str] = Field(None, description="恢复互锁动作")


class OPControlLogicInDB(OPControlLogicBase):
    """数据库中的OP控制逻辑模式"""

    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OPControlLogicResponse(OPControlLogicInDB):
    """OP控制逻辑响应模式"""

    pass


class OPControlLogicListResponse(BaseModel):
    """OP控制逻辑列表响应模式"""

    items: list[OPControlLogicResponse]
    total: int
    page: int
    size: int
    pages: int

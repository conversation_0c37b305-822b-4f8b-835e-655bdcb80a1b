"""
执行器变量映射的Pydantic模式
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class ActuatorVariableMapBase(BaseModel):
    """执行器变量映射基础模式"""

    parent_struct: Optional[str] = Field(None, description="父结构体")
    actuator: Optional[str] = Field(None, description="执行器")
    command_variable: Optional[str] = Field(None, description="命令变量")
    command_description_cn: Optional[str] = Field(None, description="命令描述(中文)")


class ActuatorVariableMapCreate(ActuatorVariableMapBase):
    """创建执行器变量映射的模式"""

    pass


class ActuatorVariableMapUpdate(BaseModel):
    """更新执行器变量映射的模式"""

    parent_struct: Optional[str] = Field(None, description="父结构体")
    actuator: Optional[str] = Field(None, description="执行器")
    command_variable: Optional[str] = Field(None, description="命令变量")
    command_description_cn: Optional[str] = Field(None, description="命令描述(中文)")


class ActuatorVariableMapInDB(ActuatorVariableMapBase):
    """数据库中的执行器变量映射模式"""

    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ActuatorVariableMapResponse(ActuatorVariableMapInDB):
    """执行器变量映射响应模式"""

    pass


class ActuatorVariableMapListResponse(BaseModel):
    """执行器变量映射列表响应模式"""

    items: list[ActuatorVariableMapResponse]
    total: int
    page: int
    size: int
    pages: int

"""
通用的Pydantic模式
"""

from typing import Any, Optional
from pydantic import BaseModel, Field


class ResponseModel(BaseModel):
    """通用响应模式"""

    success: bool = Field(True, description="操作是否成功")
    message: str = Field("", description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


class ErrorResponse(BaseModel):
    """错误响应模式"""

    success: bool = Field(False, description="操作是否成功")
    message: str = Field(..., description="错误消息")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Any] = Field(None, description="错误详情")


class PaginationParams(BaseModel):
    """分页参数模式"""

    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=1000, description="每页大小")

    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size


class CodeGenerationRequest(BaseModel):
    """代码生成请求模式"""

    table_type: str = Field(..., description="表类型: tp 或 op")
    em_description: Optional[str] = Field(None, description="EM描述筛选")
    include_test: bool = Field(False, description="是否包含测试代码")


class CodeGenerationResponse(BaseModel):
    """代码生成响应模式"""

    code: str = Field(..., description="生成的代码")
    filename: str = Field(..., description="建议的文件名")
    warnings: list[str] = Field(default_factory=list, description="警告信息")
    unmapped_conditions: list[str] = Field(
        default_factory=list, description="未映射的条件"
    )


class BatchOperationRequest(BaseModel):
    """批量操作请求模式"""

    operation: str = Field(..., description="操作类型: create, update, delete")
    data: list[dict] = Field(..., description="操作数据")


class BatchOperationResponse(BaseModel):
    """批量操作响应模式"""

    success_count: int = Field(..., description="成功操作数量")
    error_count: int = Field(..., description="失败操作数量")
    errors: list[dict] = Field(default_factory=list, description="错误详情")

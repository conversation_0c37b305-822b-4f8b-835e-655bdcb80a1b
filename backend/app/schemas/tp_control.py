"""
TP控制逻辑的Pydantic模式
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class TPControlLogicBase(BaseModel):
    """TP控制逻辑基础模式"""

    step_id: int = Field(..., description="步骤ID")
    step_description: Optional[str] = Field(None, description="步骤描述")
    motion_condition: Optional[str] = Field(None, description="运动条件")
    motion_action: Optional[str] = Field(None, description="运动动作")
    skip_condition: Optional[str] = Field(None, description="跳步条件")
    skip_step: Optional[str] = Field(None, description="跳步目标")
    tp_sequence: Optional[str] = Field(None, description="TP序列")
    em_description_cn: Optional[str] = Field(None, description="EM描述(中文)")
    em_variable: Optional[str] = Field(None, description="EM变量")
    op_sequence_under_em: Optional[str] = Field(None, description="EM下的OP序列")


class TPControlLogicCreate(TPControlLogicBase):
    """创建TP控制逻辑的模式"""

    pass


class TPControlLogicUpdate(BaseModel):
    """更新TP控制逻辑的模式"""

    step_id: Optional[int] = Field(None, description="步骤ID")
    step_description: Optional[str] = Field(None, description="步骤描述")
    motion_condition: Optional[str] = Field(None, description="运动条件")
    motion_action: Optional[str] = Field(None, description="运动动作")
    skip_condition: Optional[str] = Field(None, description="跳步条件")
    skip_step: Optional[str] = Field(None, description="跳步目标")
    tp_sequence: Optional[str] = Field(None, description="TP序列")
    em_description_cn: Optional[str] = Field(None, description="EM描述(中文)")
    em_variable: Optional[str] = Field(None, description="EM变量")
    op_sequence_under_em: Optional[str] = Field(None, description="EM下的OP序列")


class TPControlLogicInDB(TPControlLogicBase):
    """数据库中的TP控制逻辑模式"""

    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TPControlLogicResponse(TPControlLogicInDB):
    """TP控制逻辑响应模式"""

    pass


class TPControlLogicListResponse(BaseModel):
    """TP控制逻辑列表响应模式"""

    items: list[TPControlLogicResponse]
    total: int
    page: int
    size: int
    pages: int

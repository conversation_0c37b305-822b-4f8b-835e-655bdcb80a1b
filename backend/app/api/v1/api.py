"""
API v1 路由汇总
"""

from fastapi import APIRouter

from .tp_control import router as tp_control_router
from .op_control import router as op_control_router
from .actuator_map import router as actuator_map_router
from .code_generation import router as code_generation_router

api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(tp_control_router, prefix="/tp-control", tags=["TP控制逻辑"])

api_router.include_router(op_control_router, prefix="/op-control", tags=["OP控制逻辑"])

api_router.include_router(
    actuator_map_router, prefix="/actuator-map", tags=["执行器变量映射"]
)

api_router.include_router(
    code_generation_router, prefix="/code-generation", tags=["代码生成"]
)

"""
TP控制逻辑API路由
"""

from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
import math

from ...api.deps import get_database, get_pagination_params
from ...crud.tp_control import tp_control
from ...schemas.tp_control import (
    TPControlLogicCreate,
    TPControlLogicUpdate,
    TPControlLogicResponse,
    TPControlLogicListResponse,
)
from ...schemas.common import ResponseModel, PaginationParams

router = APIRouter()


@router.get("/", response_model=TPControlLogicListResponse)
def get_tp_control_list(
    db: Session = Depends(get_database),
    pagination: PaginationParams = Depends(get_pagination_params),
    step_id: Optional[int] = Query(None, description="步骤ID筛选"),
    em_description: Optional[str] = Query(None, description="EM描述筛选"),
    em_variable: Optional[str] = Query(None, description="EM变量筛选"),
) -> Any:
    """获取TP控制逻辑列表"""

    # 构建筛选条件
    filters = {}
    if step_id is not None:
        filters["step_id"] = str(step_id)
    if em_description:
        filters["em_description_cn"] = em_description
    if em_variable:
        filters["em_variable"] = em_variable

    # 获取数据
    items = tp_control.get_multi(
        db, skip=pagination.offset, limit=pagination.size, filters=filters
    )
    total = tp_control.count(db, filters=filters)
    pages = math.ceil(total / pagination.size)

    return TPControlLogicListResponse(
        items=items,
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=pages,
    )


@router.get("/{tp_id}", response_model=TPControlLogicResponse)
def get_tp_control(tp_id: int, db: Session = Depends(get_database)) -> Any:
    """获取单个TP控制逻辑记录"""

    tp_record = tp_control.get(db, id=tp_id)
    if not tp_record:
        raise HTTPException(status_code=404, detail="TP控制逻辑记录不存在")
    return tp_record


@router.post("/", response_model=TPControlLogicResponse)
def create_tp_control(
    tp_in: TPControlLogicCreate, db: Session = Depends(get_database)
) -> Any:
    """创建TP控制逻辑记录"""

    # 检查步骤ID是否已存在
    existing = tp_control.get_by_step_id(db, step_id=tp_in.step_id)
    if existing:
        raise HTTPException(status_code=400, detail=f"步骤ID {tp_in.step_id} 已存在")

    tp_record = tp_control.create(db, obj_in=tp_in)
    return tp_record


@router.put("/{tp_id}", response_model=TPControlLogicResponse)
def update_tp_control(
    tp_id: int, tp_in: TPControlLogicUpdate, db: Session = Depends(get_database)
) -> Any:
    """更新TP控制逻辑记录"""

    tp_record = tp_control.get(db, id=tp_id)
    if not tp_record:
        raise HTTPException(status_code=404, detail="TP控制逻辑记录不存在")

    # 如果更新步骤ID，检查是否与其他记录冲突
    if tp_in.step_id is not None and tp_in.step_id != tp_record.step_id:
        existing = tp_control.get_by_step_id(db, step_id=tp_in.step_id)
        if existing and existing.id != tp_id:
            raise HTTPException(
                status_code=400, detail=f"步骤ID {tp_in.step_id} 已存在"
            )

    tp_record = tp_control.update(db, db_obj=tp_record, obj_in=tp_in)
    return tp_record


@router.delete("/{tp_id}", response_model=ResponseModel)
def delete_tp_control(tp_id: int, db: Session = Depends(get_database)) -> Any:
    """删除TP控制逻辑记录"""

    tp_record = tp_control.get(db, id=tp_id)
    if not tp_record:
        raise HTTPException(status_code=404, detail="TP控制逻辑记录不存在")

    tp_control.remove(db, id=tp_id)
    return ResponseModel(success=True, message="TP控制逻辑记录删除成功")


@router.get("/grouped/by-em", response_model=ResponseModel)
def get_tp_control_grouped_by_em(db: Session = Depends(get_database)) -> Any:
    """按EM描述分组获取TP控制逻辑"""

    grouped_data = tp_control.get_grouped_by_em(db)
    return ResponseModel(success=True, message="获取分组数据成功", data=grouped_data)


@router.get("/flow/{em_variable}/{op_sequence}", response_model=ResponseModel)
def get_tp_flow(
    em_variable: str,
    op_sequence: str,
    db: Session = Depends(get_database)
) -> Any:
    """获取指定EM和OP的TP流程数据"""

    # 构建筛选条件
    filters = {
        "em_variable": em_variable,
        "op_sequence_under_em": op_sequence
    }

    # 获取数据并按step_id排序
    items = tp_control.get_multi(db, skip=0, limit=1000, filters=filters)
    # 手动排序确保顺序正确
    items = sorted(items, key=lambda x: x.step_id)

    return ResponseModel(
        success=True,
        message="获取TP流程数据成功",
        data=items
    )


@router.delete("/flow/{em_variable}/{op_sequence}", response_model=ResponseModel)
def delete_tp_flow(
    em_variable: str,
    op_sequence: str,
    db: Session = Depends(get_database)
) -> Any:
    """删除指定EM和OP的所有TP流程数据"""

    # 构建筛选条件
    filters = {
        "em_variable": em_variable,
        "op_sequence_under_em": op_sequence
    }

    # 获取要删除的记录
    items = tp_control.get_multi(db, skip=0, limit=1000, filters=filters)

    # 删除所有记录
    deleted_count = 0
    for item in items:
        tp_control.remove(db, id=item.id)
        deleted_count += 1

    return ResponseModel(
        success=True,
        message=f"删除TP流程数据成功，共删除 {deleted_count} 条记录"
    )


@router.post("/flow/batch", response_model=ResponseModel)
def create_tp_flow_batch(
    tp_list: list[TPControlLogicCreate],
    db: Session = Depends(get_database)
) -> Any:
    """批量创建TP流程数据"""

    created_items = []
    for tp_in in tp_list:
        # 检查步骤ID是否已存在（在相同EM和OP下）
        existing = db.query(tp_control.model).filter(
            tp_control.model.step_id == tp_in.step_id,
            tp_control.model.em_variable == tp_in.em_variable,
            tp_control.model.op_sequence_under_em == tp_in.op_sequence_under_em
        ).first()

        if existing:
            # 如果存在，更新记录
            tp_record = tp_control.update(db, db_obj=existing, obj_in=tp_in)
        else:
            # 如果不存在，创建新记录
            tp_record = tp_control.create(db, obj_in=tp_in)

        created_items.append(tp_record)

    return ResponseModel(
        success=True,
        message=f"批量创建TP流程数据成功，共处理 {len(created_items)} 条记录",
        data=created_items
    )

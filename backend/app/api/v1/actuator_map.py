"""
执行器变量映射API路由
"""

from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
import math

from ...api.deps import get_database, get_pagination_params
from ...crud.actuator_map import actuator_map
from ...schemas.actuator_map import (
    ActuatorVariableMapCreate,
    ActuatorVariableMapUpdate,
    ActuatorVariableMapResponse,
    ActuatorVariableMapListResponse,
)
from ...schemas.common import ResponseModel, PaginationParams

router = APIRouter()


@router.get("/", response_model=ActuatorVariableMapListResponse)
def get_actuator_map_list(
    db: Session = Depends(get_database),
    pagination: PaginationParams = Depends(get_pagination_params),
    parent_struct: Optional[str] = Query(None, description="父结构体筛选"),
    actuator: Optional[str] = Query(None, description="执行器筛选"),
    keyword: Optional[str] = Query(None, description="描述关键字搜索"),
    command_variable: Optional[str] = Query(None, description="命令变量筛选"),
    regex_search: bool = Query(False, description="是否使用正则表达式搜索"),
    advanced_search: bool = Query(False, description="是否使用高级搜索"),
) -> Any:
    """获取执行器变量映射列表"""

    if advanced_search:
        # 使用高级搜索
        items = actuator_map.advanced_search(
            db,
            keyword=keyword,
            parent_struct=parent_struct,
            actuator=actuator,
            command_variable=command_variable,
            regex_search=regex_search,
        )

        # 手动分页
        total = len(items)
        start_idx = pagination.offset
        end_idx = start_idx + pagination.size
        items = items[start_idx:end_idx]
        pages = math.ceil(total / pagination.size)
    else:
        # 使用基础搜索
        filters = {}
        if parent_struct:
            filters["parent_struct"] = parent_struct
        if actuator:
            filters["actuator"] = actuator
        if keyword:
            filters["command_description_cn"] = keyword

        # 获取数据
        items = actuator_map.get_multi(
            db, skip=pagination.offset, limit=pagination.size, filters=filters
        )
        total = actuator_map.count(db, filters=filters)
        pages = math.ceil(total / pagination.size)

    return ActuatorVariableMapListResponse(
        items=items,
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=pages,
    )


@router.get("/{map_id}", response_model=ActuatorVariableMapResponse)
def get_actuator_map(map_id: int, db: Session = Depends(get_database)) -> Any:
    """获取单个执行器变量映射记录"""

    map_record = actuator_map.get(db, id=map_id)
    if not map_record:
        raise HTTPException(status_code=404, detail="执行器变量映射记录不存在")
    return map_record


@router.post("/", response_model=ActuatorVariableMapResponse)
def create_actuator_map(
    map_in: ActuatorVariableMapCreate, db: Session = Depends(get_database)
) -> Any:
    """创建执行器变量映射记录"""

    # 数据完整性检查
    if not map_in.command_description_cn or not map_in.command_description_cn.strip():
        raise HTTPException(status_code=400, detail="命令描述不能为空")

    if not map_in.command_variable or not map_in.command_variable.strip():
        raise HTTPException(status_code=400, detail="命令变量不能为空")

    # 检查命令描述是否已存在
    existing = actuator_map.get_by_command_description(
        db, description=map_in.command_description_cn.strip()
    )
    if existing:
        raise HTTPException(
            status_code=400, detail=f"命令描述 '{map_in.command_description_cn}' 已存在"
        )

    # 验证parent_struct和actuator字段的有效值
    valid_flags = ["Y", "N", "OP"]
    if map_in.parent_struct and map_in.parent_struct not in valid_flags:
        # 如果不是标志位，应该是具体的结构体名称，允许通过
        pass

    if map_in.actuator and map_in.actuator not in valid_flags:
        # 如果不是标志位，应该是具体的执行器名称，允许通过
        pass

    map_record = actuator_map.create(db, obj_in=map_in)
    return map_record


@router.put("/{map_id}", response_model=ActuatorVariableMapResponse)
def update_actuator_map(
    map_id: int, map_in: ActuatorVariableMapUpdate, db: Session = Depends(get_database)
) -> Any:
    """更新执行器变量映射记录"""

    map_record = actuator_map.get(db, id=map_id)
    if not map_record:
        raise HTTPException(status_code=404, detail="执行器变量映射记录不存在")

    # 数据完整性检查
    if map_in.command_description_cn is not None:
        if not map_in.command_description_cn.strip():
            raise HTTPException(status_code=400, detail="命令描述不能为空")

        # 检查是否与其他记录冲突
        existing = actuator_map.get_by_command_description(
            db, description=map_in.command_description_cn.strip()
        )
        if existing and existing.id != map_id:
            raise HTTPException(
                status_code=400,
                detail=f"命令描述 '{map_in.command_description_cn}' 已存在",
            )

    if map_in.command_variable is not None:
        if not map_in.command_variable.strip():
            raise HTTPException(status_code=400, detail="命令变量不能为空")

    # 验证parent_struct和actuator字段的有效值
    valid_flags = ["Y", "N", "OP"]
    if map_in.parent_struct is not None and map_in.parent_struct not in valid_flags:
        # 如果不是标志位，应该是具体的结构体名称，允许通过
        pass

    if map_in.actuator is not None and map_in.actuator not in valid_flags:
        # 如果不是标志位，应该是具体的执行器名称，允许通过
        pass

    map_record = actuator_map.update(db, db_obj=map_record, obj_in=map_in)
    return map_record


@router.delete("/{map_id}", response_model=ResponseModel)
def delete_actuator_map(map_id: int, db: Session = Depends(get_database)) -> Any:
    """删除执行器变量映射记录"""

    map_record = actuator_map.get(db, id=map_id)
    if not map_record:
        raise HTTPException(status_code=404, detail="执行器变量映射记录不存在")

    actuator_map.remove(db, id=map_id)
    return ResponseModel(success=True, message="执行器变量映射记录删除成功")


@router.get("/mapping/dict", response_model=ResponseModel)
def get_mapping_dict(db: Session = Depends(get_database)) -> Any:
    """获取映射字典，用于代码生成"""

    mapping_dict = actuator_map.get_mapping_dict(db)
    return ResponseModel(success=True, message="获取映射字典成功", data=mapping_dict)


@router.post("/search/condition", response_model=ResponseModel)
def search_by_condition(
    condition_text: str, db: Session = Depends(get_database)
) -> Any:
    """根据条件文本搜索相关映射"""

    results = actuator_map.search_by_condition_pattern(db, condition_text)
    return ResponseModel(
        success=True,
        message=f"找到 {len(results)} 条相关映射",
        data=[
            {
                "id": r.id,
                "parent_struct": r.parent_struct,
                "actuator": r.actuator,
                "command_variable": r.command_variable,
                "command_description_cn": r.command_description_cn,
            }
            for r in results
        ],
    )


@router.post("/mapping/find", response_model=ResponseModel)
def find_condition_mapping(
    condition_text: str, step_data: dict, db: Session = Depends(get_database)
) -> Any:
    """查找条件的映射变量路径"""

    result = actuator_map.find_matching_condition(db, condition_text, step_data)

    if result:
        return ResponseModel(
            success=True,
            message="找到匹配的映射",
            data={"condition": condition_text, "mapped_variable": result},
        )
    else:
        return ResponseModel(
            success=False,
            message="未找到匹配的映射",
            data={"condition": condition_text, "mapped_variable": None},
        )


@router.get("/extract/actuator", response_model=ResponseModel)
def extract_actuator_name(
    text: str = Query(..., description="要提取执行器名称的文本"),
) -> Any:
    """从文本中提取执行器名称"""

    extracted = actuator_map.extract_actuator_name(text)
    return ResponseModel(
        success=True,
        message="执行器名称提取成功",
        data={"original_text": text, "extracted_actuator": extracted},
    )


@router.get("/suggestions/{condition_text}", response_model=ResponseModel)
def get_condition_suggestions(
    condition_text: str, db: Session = Depends(get_database)
) -> Any:
    """获取条件映射建议"""
    from ...services.code_generator import code_generator_service

    suggestions = code_generator_service.get_condition_mapping_suggestions(
        db, condition_text
    )

    return ResponseModel(success=True, message="获取条件映射建议成功", data=suggestions)


@router.post("/validate/conditions", response_model=ResponseModel)
def validate_conditions(db: Session = Depends(get_database)) -> Any:
    """验证所有条件映射的完整性"""
    from ...services.code_generator import code_generator_service

    validation_result = code_generator_service.validate_conditions(db)

    return ResponseModel(
        success=True, message="条件映射验证完成", data=validation_result
    )

"""
代码生成API路由
"""

from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import PlainTextResponse
from sqlalchemy.orm import Session

from ...api.deps import get_database
from ...services.code_generator import code_generator_service
from ...schemas.common import (
    ResponseModel,
    CodeGenerationRequest,
    CodeGenerationResponse,
)

router = APIRouter()


@router.post("/tp", response_model=CodeGenerationResponse)
def generate_tp_code(
    request: CodeGenerationRequest, db: Session = Depends(get_database)
) -> Any:
    """生成TP代码"""

    if request.table_type != "tp":
        raise HTTPException(status_code=400, detail="表类型必须为 'tp'")

    try:
        code, warnings, unmapped = code_generator_service.generate_tp_code(
            db, em_description=request.em_description
        )

        # 生成文件名
        filename = "generated_tp_code.txt"
        if request.em_description:
            safe_name = "".join(
                c for c in request.em_description if c.isalnum() or c in "._-"
            )
            filename = f"tp_code_{safe_name}.txt"

        return CodeGenerationResponse(
            code=code,
            filename=filename,
            warnings=warnings,
            unmapped_conditions=unmapped,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"TP代码生成失败: {str(e)}")


@router.post("/op", response_model=CodeGenerationResponse)
def generate_op_code(
    request: CodeGenerationRequest, db: Session = Depends(get_database)
) -> Any:
    """生成OP代码"""

    if request.table_type != "op":
        raise HTTPException(status_code=400, detail="表类型必须为 'op'")

    try:
        code, warnings, unmapped = code_generator_service.generate_op_code(
            db, em_description=request.em_description
        )

        # 生成文件名
        filename = "generated_op_code.txt"
        if request.em_description:
            safe_name = "".join(
                c for c in request.em_description if c.isalnum() or c in "._-"
            )
            filename = f"op_code_{safe_name}.txt"

        return CodeGenerationResponse(
            code=code,
            filename=filename,
            warnings=warnings,
            unmapped_conditions=unmapped,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"OP代码生成失败: {str(e)}")


@router.get("/tp/download")
def download_tp_code(
    em_description: Optional[str] = Query(None, description="EM描述筛选"),
    db: Session = Depends(get_database),
) -> PlainTextResponse:
    """下载TP代码文件"""

    try:
        code, _, _ = code_generator_service.generate_tp_code(
            db, em_description=em_description
        )

        # 生成文件名
        filename = "generated_tp_code.txt"
        if em_description:
            safe_name = "".join(c for c in em_description if c.isalnum() or c in "._-")
            filename = f"tp_code_{safe_name}.txt"

        return PlainTextResponse(
            content=code,
            headers={"Content-Disposition": f"attachment; filename={filename}"},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"TP代码下载失败: {str(e)}")


@router.get("/op/download")
def download_op_code(
    em_description: Optional[str] = Query(None, description="EM描述筛选"),
    db: Session = Depends(get_database),
) -> PlainTextResponse:
    """下载OP代码文件"""

    try:
        code, _, _ = code_generator_service.generate_op_code(
            db, em_description=em_description
        )

        # 生成文件名
        filename = "generated_op_code.txt"
        if em_description:
            safe_name = "".join(c for c in em_description if c.isalnum() or c in "._-")
            filename = f"op_code_{safe_name}.txt"

        return PlainTextResponse(
            content=code,
            headers={"Content-Disposition": f"attachment; filename={filename}"},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"OP代码下载失败: {str(e)}")


@router.get("/validate", response_model=ResponseModel)
def validate_conditions(
    db: Session = Depends(get_database),
    detailed: bool = Query(False, description="是否返回详细分析"),
) -> Any:
    """验证条件映射完整性"""

    try:
        validation_result = code_generator_service.validate_conditions(db)

        if detailed:
            # 添加详细的条件分析
            detailed_analysis = code_generator_service.analyze_conditions_detailed(db)
            validation_result.update(detailed_analysis)

        return ResponseModel(
            success=True, message="条件映射验证完成", data=validation_result
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"条件映射验证失败: {str(e)}")


@router.post("/test-condition", response_model=ResponseModel)
def test_condition_mapping(
    condition_text: str,
    step_data: Optional[dict] = None,
    db: Session = Depends(get_database),
) -> Any:
    """测试单个条件的映射结果"""

    try:
        if not step_data:
            step_data = {
                "em_variable": "io_stEmBtryIn",
                "op_sequence_under_em": "OP[1]",
            }

        # 测试条件映射
        mapping_result = code_generator_service.find_condition_mapping(
            db, condition_text, step_data
        )

        # 获取映射建议
        suggestions = code_generator_service.get_condition_mapping_suggestions(
            db, condition_text
        )

        return ResponseModel(
            success=True,
            message="条件映射测试完成",
            data={
                "condition": condition_text,
                "step_data": step_data,
                "mapped_result": mapping_result,
                "suggestions": suggestions,
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"条件映射测试失败: {str(e)}")

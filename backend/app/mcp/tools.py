"""
MCP工具函数定义
使用@mcp.tool()装饰器定义的工具函数
"""

from typing import Any, Dict, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from fastmcp import FastMCP, Context

from ..api.deps import get_database
from ..services.code_generator import code_generator_service
from ..crud.tp_control import tp_control
from ..crud.op_control import op_control
from ..crud.actuator_map import actuator_map


class MockContext:
    """模拟的MCP Context对象"""

    def __init__(self):
        self.logs = []

    async def info(self, message: str):
        """记录信息日志"""
        self.logs.append(f"INFO: {message}")
        print(f"MCP INFO: {message}")

    async def error(self, message: str):
        """记录错误日志"""
        self.logs.append(f"ERROR: {message}")
        print(f"MCP ERROR: {message}")

    async def debug(self, message: str):
        """记录调试日志"""
        self.logs.append(f"DEBUG: {message}")
        print(f"MCP DEBUG: {message}")


mcp = FastMCP()


# 定义输入输出模型
class QueryTPControlInput(BaseModel):
    step_id: Optional[int] = Field(None, description="步骤ID筛选")
    em_description: Optional[str] = Field(None, description="EM描述筛选")
    em_variable: Optional[str] = Field(None, description="EM变量筛选")
    limit: int = Field(50, description="返回记录数限制")


class QueryOPControlInput(BaseModel):
    step_id: Optional[int] = Field(None, description="步骤ID筛选")
    em_description: Optional[str] = Field(None, description="EM描述筛选")
    em_variable: Optional[str] = Field(None, description="EM变量筛选")
    limit: int = Field(50, description="返回记录数限制")


class QueryActuatorMapInput(BaseModel):
    command_description: Optional[str] = Field(None, description="命令描述筛选")
    command_variable: Optional[str] = Field(None, description="命令变量筛选")
    limit: int = Field(50, description="返回记录数限制")


class GenerateCodeInput(BaseModel):
    em_description: Optional[str] = Field(
        None, description="EM描述筛选，用于生成特定EM的代码"
    )


class ValidateConditionsInput(BaseModel):
    detailed: bool = Field(False, description="是否返回详细分析")


class TestConditionMappingInput(BaseModel):
    condition_text: str = Field(..., description="要测试的条件文本")
    step_data: Dict[str, str] = Field(
        default_factory=lambda: {
            "em_variable": "io_stEmBtryIn",
            "op_sequence_under_em": "OP[1]",
        },
        description="步骤数据上下文",
    )


class PLCQueryResult(BaseModel):
    success: bool
    total: int
    items: List[Dict[str, Any]]
    message: Optional[str] = None
    error: Optional[str] = None


class CodeGenerationResult(BaseModel):
    success: bool
    code: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)
    unmapped_conditions: List[str] = Field(default_factory=list)
    generated_at: str
    error: Optional[str] = None


class ValidationResult(BaseModel):
    success: bool
    validation_data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


# MCP工具函数
@mcp.tool()
async def query_tp_control(ctx: Context, inputs: QueryTPControlInput) -> PLCQueryResult:
    """
    查询TP控制逻辑数据

    Args:
        ctx: FastMCP上下文对象
        inputs: 查询参数，包含step_id、em_description、em_variable、limit

    Returns:
        PLCQueryResult: 查询结果，包含总数、数据项列表等
    """
    await ctx.info(f"查询TP控制逻辑: {inputs.model_dump()}")

    try:
        db = next(get_database())

        filters = {}
        if inputs.step_id is not None:
            filters["step_id"] = str(inputs.step_id)
        if inputs.em_description:
            filters["em_description_cn"] = inputs.em_description
        if inputs.em_variable:
            filters["em_variable"] = inputs.em_variable

        items = tp_control.get_multi(db, skip=0, limit=inputs.limit, filters=filters)
        total = tp_control.count(db, filters=filters)

        result_items = [
            {
                "id": item.id,
                "step_id": item.step_id,
                "step_description": item.step_description,
                "motion_condition": item.motion_condition,
                "motion_action": item.motion_action,
                "skip_condition": item.skip_condition,
                "skip_step": item.skip_step,
                "tp_sequence": item.tp_sequence,
                "em_description_cn": item.em_description_cn,
                "em_variable": item.em_variable,
                "op_sequence_under_em": item.op_sequence_under_em,
            }
            for item in items
        ]

        await ctx.info(f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录")

        return PLCQueryResult(
            success=True,
            total=total,
            items=result_items,
            message=f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录",
        )

    except Exception as e:
        await ctx.error(f"查询TP控制逻辑失败: {e}")
        return PLCQueryResult(success=False, total=0, items=[], error=str(e))
    finally:
        db.close()


@mcp.tool()
async def query_op_control(ctx: Context, inputs: QueryOPControlInput) -> PLCQueryResult:
    """
    查询OP控制逻辑数据

    Args:
        ctx: FastMCP上下文对象
        inputs: 查询参数，包含step_id、em_description、em_variable、limit

    Returns:
        PLCQueryResult: 查询结果，包含总数、数据项列表等
    """
    await ctx.info(f"查询OP控制逻辑: {inputs.model_dump()}")

    try:
        db = next(get_database())

        filters = {}
        if inputs.step_id is not None:
            filters["step_id"] = str(inputs.step_id)
        if inputs.em_description:
            filters["em_description_cn"] = inputs.em_description
        if inputs.em_variable:
            filters["em_variable"] = inputs.em_variable

        items = op_control.get_multi(db, skip=0, limit=inputs.limit, filters=filters)
        total = op_control.count(db, filters=filters)

        result_items = [
            {
                "id": item.id,
                "step_id": item.step_id,
                "step_description": item.step_description,
                "watchdog_mask": item.watchdog_mask,
                "action_description": item.action_description,
                "normal_interlock_condition": item.normal_interlock_condition,
                "recovery_interlock_condition": item.recovery_interlock_condition,
                "recovery_interlock_action": item.recovery_interlock_action,
                "alarm_condition": item.alarm_condition,
                "skip_condition": item.skip_condition,
                "em_description_cn": item.em_description_cn,
                "em_variable": item.em_variable,
                "op_sequence_under_em": item.op_sequence_under_em,
            }
            for item in items
        ]

        await ctx.info(f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录")

        return PLCQueryResult(
            success=True,
            total=total,
            items=result_items,
            message=f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录",
        )

    except Exception as e:
        await ctx.error(f"查询OP控制逻辑失败: {e}")
        return PLCQueryResult(success=False, total=0, items=[], error=str(e))
    finally:
        db.close()


@mcp.tool()
async def query_actuator_map(
    ctx: Context, inputs: QueryActuatorMapInput
) -> PLCQueryResult:
    """
    查询执行器变量映射数据

    Args:
        ctx: FastMCP上下文对象
        inputs: 查询参数，包含command_description、variable_name、limit

    Returns:
        PLCQueryResult: 查询结果，包含总数、数据项列表等
    """
    await ctx.info(f"查询执行器变量映射: {inputs.model_dump()}")

    try:
        db = next(get_database())

        filters = {}
        if inputs.command_description:
            filters["command_description_cn"] = inputs.command_description
        if inputs.command_variable:
            filters["command_variable"] = inputs.command_variable

        items = actuator_map.get_multi(db, skip=0, limit=inputs.limit, filters=filters)
        total = actuator_map.count(db, filters=filters)

        result_items = [
            {
                "id": item.id,
                "parent_struct": item.parent_struct,
                "actuator": item.actuator,
                "command_variable": item.command_variable,
                "command_description_cn": item.command_description_cn,
            }
            for item in items
        ]

        await ctx.info(f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录")

        return PLCQueryResult(
            success=True,
            total=total,
            items=result_items,
            message=f"查询完成: 总数 {total}, 返回 {len(result_items)} 条记录",
        )

    except Exception as e:
        await ctx.error(f"查询执行器变量映射失败: {e}")
        return PLCQueryResult(success=False, total=0, items=[], error=str(e))
    finally:
        db.close()


@mcp.tool()
async def generate_tp_code(
    ctx: Context, inputs: GenerateCodeInput
) -> CodeGenerationResult:
    """生成TP代码"""
    await ctx.info(f"生成TP代码: {inputs.model_dump()}")

    try:
        db = next(get_database())
        code, warnings, unmapped = code_generator_service.generate_tp_code(
            db, em_description=inputs.em_description
        )
        await ctx.info(
            f"TP代码生成完成: 警告 {len(warnings)} 个, 未映射条件 {len(unmapped)} 个"
        )

        return CodeGenerationResult(
            success=True,
            code=code,
            warnings=warnings,
            unmapped_conditions=unmapped,
            generated_at=datetime.now().isoformat(),
        )
    except Exception as e:
        await ctx.error(f"TP代码生成失败: {e}")
        return CodeGenerationResult(
            success=False, generated_at=datetime.now().isoformat(), error=str(e)
        )
    finally:
        db.close()


@mcp.tool()
async def generate_op_code(
    ctx: Context, inputs: GenerateCodeInput
) -> CodeGenerationResult:
    """生成OP代码"""
    await ctx.info(f"生成OP代码: {inputs.model_dump()}")

    try:
        db = next(get_database())
        code, warnings, unmapped = code_generator_service.generate_op_code(
            db, em_description=inputs.em_description
        )
        await ctx.info(
            f"OP代码生成完成: 警告 {len(warnings)} 个, 未映射条件 {len(unmapped)} 个"
        )

        return CodeGenerationResult(
            success=True,
            code=code,
            warnings=warnings,
            unmapped_conditions=unmapped,
            generated_at=datetime.now().isoformat(),
        )
    except Exception as e:
        await ctx.error(f"OP代码生成失败: {e}")
        return CodeGenerationResult(
            success=False, generated_at=datetime.now().isoformat(), error=str(e)
        )
    finally:
        db.close()


@mcp.tool()
async def validate_conditions(
    ctx: Context, inputs: ValidateConditionsInput
) -> ValidationResult:
    """验证条件映射完整性"""
    await ctx.info(f"验证条件映射: {inputs.model_dump()}")

    try:
        db = next(get_database())
        validation_result = code_generator_service.validate_conditions(db)

        if inputs.detailed:
            detailed_analysis = code_generator_service.analyze_conditions_detailed(db)
            validation_result.update(detailed_analysis)

        await ctx.info("条件映射验证完成")
        return ValidationResult(success=True, validation_data=validation_result)
    except Exception as e:
        await ctx.error(f"条件映射验证失败: {e}")
        return ValidationResult(success=False, error=str(e))
    finally:
        db.close()


@mcp.tool()
async def test_condition_mapping(
    ctx: Context, inputs: TestConditionMappingInput
) -> ValidationResult:
    """测试单个条件的映射结果"""
    await ctx.info(f"测试条件映射: {inputs.model_dump()}")

    try:
        db = next(get_database())
        mapping_result = code_generator_service.find_condition_mapping(
            db, inputs.condition_text, inputs.step_data
        )
        suggestions = code_generator_service.get_condition_mapping_suggestions(
            db, inputs.condition_text
        )

        result_data = {
            "condition": inputs.condition_text,
            "step_data": inputs.step_data,
            "mapped_result": mapping_result,
            "suggestions": suggestions,
        }

        await ctx.info("条件映射测试完成")
        return ValidationResult(success=True, validation_data=result_data)
    except Exception as e:
        await ctx.error(f"条件映射测试失败: {e}")
        return ValidationResult(success=False, error=str(e))
    finally:
        db.close()


@mcp.tool()
async def get_tp_grouped_by_em(ctx: Context) -> ValidationResult:
    """按EM描述分组获取TP控制逻辑"""
    await ctx.info("按EM分组查询TP控制逻辑")

    try:
        db = next(get_database())
        grouped_data = tp_control.get_grouped_by_em(db)
        await ctx.info(f"分组查询完成: {len(grouped_data)} 个分组")

        # 将分组数据转换为适合返回的格式
        serialized_groups = {}
        for em_desc, items in grouped_data.items():
            serialized_groups[em_desc] = [
                {
                    "id": item.id,
                    "step_id": item.step_id,
                    "step_description": item.step_description,
                    "motion_condition": item.motion_condition,
                    "motion_action": item.motion_action,
                    "skip_condition": item.skip_condition,
                    "skip_step": item.skip_step,
                    "tp_sequence": item.tp_sequence,
                    "em_description_cn": item.em_description_cn,
                    "em_variable": item.em_variable,
                    "op_sequence_under_em": item.op_sequence_under_em,
                }
                for item in items
            ]

        result_data = {"grouped_count": len(grouped_data), "groups": serialized_groups}

        return ValidationResult(success=True, validation_data=result_data)
    except Exception as e:
        await ctx.error(f"分组查询失败: {e}")
        return ValidationResult(success=False, error=str(e))
    finally:
        db.close()

"""
OP控制逻辑数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.sql import func

from ..core.database import Base


class OPControlLogic(Base):
    """OP控制逻辑表模型"""

    __tablename__ = "op_control_logic"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    step_id = Column(
        String(100), nullable=False, comment="步骤ID"
    )  # 注意：数据库中是varchar(100)
    step_description = Column(String(200), comment="步骤描述")
    watchdog_mask = Column(String(100), comment="看门狗掩码")
    action_description = Column(String(200), comment="动作描述")
    normal_interlock_condition = Column(String(300), comment="正常互锁条件")
    recovery_interlock_condition = Column(String(300), comment="恢复互锁条件")
    recovery_interlock_action = Column(String(100), comment="恢复互锁动作")
    alarm_condition = Column(String(200), comment="报警条件")
    skip_condition = Column(String(200), comment="跳步条件")
    em_description_cn = Column(String(100), comment="EM描述(中文)")
    op_sequence_under_em = Column(String(100), comment="EM下的OP序列")
    em_variable = Column(String(100), comment="EM变量")

    # 时间戳字段
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    def __repr__(self):
        return f"<OPControlLogic(id={self.id}, step_id={self.step_id}, description='{self.step_description}')>"

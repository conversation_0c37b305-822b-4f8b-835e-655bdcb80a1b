"""
TP控制逻辑数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.sql import func

from ..core.database import Base


class TPControlLogic(Base):
    """TP控制逻辑表模型"""

    __tablename__ = "tp_control_logic"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    step_id = Column(Integer, nullable=False, comment="步骤ID")
    step_description = Column(String(50), comment="步骤描述")
    motion_condition = Column(String(50), comment="运动条件")
    motion_action = Column(String(50), comment="运动动作")
    skip_condition = Column(String(256), comment="跳步条件")
    skip_step = Column(String(50), comment="跳步目标")
    tp_sequence = Column(String(50), comment="TP序列")
    em_description_cn = Column(String(50), comment="EM描述(中文)")
    em_variable = Column(String(50), comment="EM变量")
    op_sequence_under_em = Column(String(50), comment="EM下的OP序列")

    # 时间戳字段
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    def __repr__(self):
        return f"<TPControlLogic(id={self.id}, step_id={self.step_id}, description='{self.step_description}')>"

"""
执行器变量映射数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.sql import func

from ..core.database import Base


class ActuatorVariableMap(Base):
    """执行器变量映射表模型"""

    __tablename__ = "actuator_variable_map"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    parent_struct = Column(String(100), comment="父结构体")
    actuator = Column(String(100), comment="执行器")
    command_variable = Column(String(100), comment="命令变量")
    command_description_cn = Column(String(200), comment="命令描述(中文)")

    # 时间戳字段
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    def __repr__(self):
        return f"<ActuatorVariableMap(id={self.id}, parent_struct='{self.parent_struct}', actuator='{self.actuator}')>"

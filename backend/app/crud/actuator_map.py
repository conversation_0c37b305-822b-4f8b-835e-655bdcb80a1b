"""
执行器变量映射CRUD操作
"""

import re
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session

from .base import CRUDBase
from ..models.actuator_map import ActuatorVariableMap
from ..schemas.actuator_map import ActuatorVariableMapCreate, ActuatorVariableMapUpdate


class CRUDActuatorVariableMap(
    CRUDBase[ActuatorVariableMap, ActuatorVariableMapCreate, ActuatorVariableMapUpdate]
):
    """执行器变量映射CRUD操作类"""

    def get_by_parent_struct(
        self, db: Session, *, parent_struct: str
    ) -> List[ActuatorVariableMap]:
        """根据父结构体获取记录列表"""
        return (
            db.query(ActuatorVariableMap)
            .filter(ActuatorVariableMap.parent_struct == parent_struct)
            .all()
        )

    def get_by_actuator(
        self, db: Session, *, actuator: str
    ) -> List[ActuatorVariableMap]:
        """根据执行器获取记录列表"""
        return (
            db.query(ActuatorVariableMap)
            .filter(ActuatorVariableMap.actuator == actuator)
            .all()
        )

    def get_by_command_description(
        self, db: Session, *, description: str
    ) -> Optional[ActuatorVariableMap]:
        """根据命令描述获取记录"""
        return (
            db.query(ActuatorVariableMap)
            .filter(ActuatorVariableMap.command_description_cn == description)
            .first()
        )

    def search_by_description(
        self, db: Session, *, keyword: str
    ) -> List[ActuatorVariableMap]:
        """根据描述关键字搜索记录"""
        return (
            db.query(ActuatorVariableMap)
            .filter(ActuatorVariableMap.command_description_cn.like(f"%{keyword}%"))
            .all()
        )

    def get_mapping_dict(self, db: Session) -> dict:
        """获取映射字典，用于代码生成"""
        records = db.query(ActuatorVariableMap).all()
        mapping = {}

        for record in records:
            if record.command_description_cn:
                mapping[record.command_description_cn] = {
                    "parent_struct": record.parent_struct,
                    "actuator": record.actuator,
                    "command_variable": record.command_variable,
                }

        return mapping

    # ========== 新增的高级搜索功能 ==========

    @staticmethod
    def extract_actuator_name(text: str) -> str:
        """从文本中提取执行器名称（如SV1、YV2等）"""
        # 匹配字母+数字的组合
        match = re.search(r"([A-Za-z]+)(\d+)", text)
        if match:
            letters = match.group(1)
            numbers = match.group(2)
            return f"{letters}[{numbers}]"
        return text

    @staticmethod
    def extract_condition_name(condition_text: str) -> str:
        """从条件文本中提取条件名称部分"""
        # 处理条件文本，如"SV1不在零点" -> "不在零点"
        match = re.search(r"[A-Za-z]+\d+(.+)", condition_text)
        if match:
            return match.group(1).strip()
        return condition_text.strip()

    def build_variable_path(
        self,
        var_mapping: Dict[str, str],
        step_data: Dict[str, Any],
        condition_text: str = "",
    ) -> Optional[str]:
        """构建变量路径，处理parent_struct和actuator的Y/N/OP逻辑"""
        parent_struct = var_mapping.get("parent_struct", "")
        actuator_flag = var_mapping.get("actuator", "")
        command_variable = var_mapping.get("command_variable", "")

        # 构建基础路径
        path_parts = []

        # 处理parent_struct：Y表示需要动态拼接EM变量，N表示使用原值
        if parent_struct == "Y":
            # 动态拼接EM变量
            em_variable = step_data.get("em_variable", "io_stEmBtryIn")
            path_parts.append(em_variable)
        elif parent_struct == "N":
            # 不拼接parent_struct
            pass
        elif parent_struct:  # 如果是具体的值，直接使用
            path_parts.append(parent_struct)

        # 处理actuator：Y表示动态检测执行器，N表示不拼接，OP表示拼接OP[N]
        if actuator_flag == "Y":
            # 动态检测执行器并拼接
            actuator_match = re.search(r"([A-Za-z]+\d+)", condition_text)
            if actuator_match:
                actuator_part = actuator_match.group(1)
                formatted_actuator = self.extract_actuator_name(actuator_part)
                path_parts.append(formatted_actuator)
            else:
                return None  # 没有找到执行器，无法构建路径
        elif actuator_flag == "OP":
            # 拼接OP[N]变量
            op_sequence = step_data.get("op_sequence_under_em", "OP[1]")
            path_parts.append(op_sequence)
        elif actuator_flag == "N":
            # 不拼接actuator
            pass

        # 添加command_variable
        if command_variable:
            path_parts.append(command_variable)

        # 组合路径
        if path_parts:
            return ".".join(path_parts)
        else:
            return command_variable if command_variable else None

    def find_matching_condition(
        self, db: Session, condition_text: str, step_data: Dict[str, Any]
    ) -> Optional[str]:
        """在映射表中查找匹配的条件"""
        # 获取所有映射记录
        mappings = self.get_mapping_dict(db)

        # 1. 直接匹配
        if condition_text in mappings:
            return self.build_variable_path(
                mappings[condition_text], step_data, condition_text
            )

        # 2. 处理正则表达式模式匹配（通用处理）
        for desc, var_path in mappings.items():
            # 检查是否是正则表达式模式（包含反斜杠转义字符）
            if "\\" in desc and "(" in desc and ")" in desc:
                try:
                    # 将数据库中的正则表达式转换为Python正则表达式
                    regex_pattern = desc.replace("\\d", r"\d").replace("\\+", "+")

                    # 尝试匹配整个条件文本
                    full_match = re.search(regex_pattern, condition_text)
                    if full_match and len(full_match.groups()) > 0:
                        # 提取数字
                        number = full_match.group(1)

                        # 创建临时映射，替换变量中的N为实际数字
                        temp_mapping = var_path.copy()
                        temp_mapping["command_variable"] = var_path[
                            "command_variable"
                        ].replace("[N]", f"[{number}]")

                        # 使用统一的路径构建方法
                        result = self.build_variable_path(
                            temp_mapping, step_data, condition_text
                        )
                        if result:
                            return result
                except re.error:
                    # 如果正则表达式有问题，跳过
                    continue

        # 3. 提取执行器和条件部分进行匹配
        actuator_match = re.search(r"([A-Za-z]+\d+)(.+)", condition_text)
        if actuator_match:
            condition_part = actuator_match.group(2).strip()  # 如 "在零点"

            # 在映射表中查找条件部分
            for desc, var_path in mappings.items():
                # 简单关键词匹配
                if condition_part in desc:
                    # 使用统一的路径构建方法
                    result = self.build_variable_path(
                        var_path, step_data, condition_text
                    )
                    if result:
                        return result

        # 如果找不到匹配，返回None
        return None

    def advanced_search(
        self,
        db: Session,
        *,
        keyword: Optional[str] = None,
        parent_struct: Optional[str] = None,
        actuator: Optional[str] = None,
        command_variable: Optional[str] = None,
        regex_search: bool = False,
    ) -> List[ActuatorVariableMap]:
        """高级搜索功能"""
        query = db.query(ActuatorVariableMap)

        # 关键字搜索
        if keyword:
            if regex_search:
                try:
                    # 正则表达式搜索
                    query = query.filter(
                        ActuatorVariableMap.command_description_cn.op("REGEXP")(keyword)
                    )
                except Exception:
                    # 如果正则表达式有问题，回退到普通搜索
                    query = query.filter(
                        ActuatorVariableMap.command_description_cn.like(f"%{keyword}%")
                    )
            else:
                # 普通模糊搜索
                query = query.filter(
                    ActuatorVariableMap.command_description_cn.like(f"%{keyword}%")
                )

        # 其他筛选条件
        if parent_struct:
            query = query.filter(ActuatorVariableMap.parent_struct == parent_struct)
        if actuator:
            query = query.filter(ActuatorVariableMap.actuator == actuator)
        if command_variable:
            query = query.filter(
                ActuatorVariableMap.command_variable.like(f"%{command_variable}%")
            )

        return query.all()

    def search_by_condition_pattern(
        self, db: Session, condition_text: str
    ) -> List[ActuatorVariableMap]:
        """根据条件模式搜索相关映射"""
        results = []

        # 1. 直接搜索
        direct_match = self.get_by_command_description(db, description=condition_text)
        if direct_match:
            results.append(direct_match)

        # 2. 提取执行器和条件部分进行搜索
        actuator_match = re.search(r"([A-Za-z]+\d+)(.+)", condition_text)
        if actuator_match:
            condition_part = actuator_match.group(2).strip()
            # 搜索包含条件部分的记录
            partial_matches = self.search_by_description(db, keyword=condition_part)
            results.extend(partial_matches)

        # 3. 搜索包含关键词的记录
        keyword_matches = self.search_by_description(db, keyword=condition_text)
        results.extend(keyword_matches)

        # 去重
        seen_ids = set()
        unique_results = []
        for result in results:
            if result.id not in seen_ids:
                seen_ids.add(result.id)
                unique_results.append(result)

        return unique_results


# 创建CRUD实例
actuator_map = CRUDActuatorVariableMap(ActuatorVariableMap)

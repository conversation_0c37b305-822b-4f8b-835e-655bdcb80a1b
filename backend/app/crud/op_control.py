"""
OP控制逻辑CRUD操作
"""

from typing import List, Optional, Dict
from sqlalchemy.orm import Session

from .base import CRUDBase
from ..models.op_control import OPControlLogic
from ..schemas.op_control import OPControlLogicCreate, OPControlLogicUpdate


class CRUDOPControlLogic(
    CRUDBase[OPControlLogic, OPControlLogicCreate, OPControlLogicUpdate]
):
    """OP控制逻辑CRUD操作类"""

    def get_by_step_id(self, db: Session, *, step_id: int) -> Optional[OPControlLogic]:
        """根据步骤ID获取记录"""
        return (
            db.query(OPControlLogic).filter(OPControlLogic.step_id == step_id).first()
        )

    def get_by_em_description(
        self, db: Session, *, em_description: str
    ) -> List[OPControlLogic]:
        """根据EM描述获取记录列表"""
        return (
            db.query(OPControlLogic)
            .filter(OPControlLogic.em_description_cn.like(f"%{em_description}%"))
            .order_by(OPControlLogic.step_id)
            .all()
        )

    def get_by_em_variable(
        self, db: Session, *, em_variable: str
    ) -> List[OPControlLogic]:
        """根据EM变量获取记录列表"""
        return (
            db.query(OPControlLogic)
            .filter(OPControlLogic.em_variable == em_variable)
            .order_by(OPControlLogic.step_id)
            .all()
        )

    def get_grouped_by_em(self, db: Session) -> dict:
        """按EM描述分组获取记录"""
        records = (
            db.query(OPControlLogic)
            .order_by(OPControlLogic.em_description_cn, OPControlLogic.step_id)
            .all()
        )

        grouped: Dict = {}
        for record in records:
            em_desc = record.em_description_cn or "未分类"
            if em_desc not in grouped:
                grouped[em_desc] = []
            # 转换为字典以便JSON序列化
            record_dict = {
                "id": record.id,
                "step_id": record.step_id,
                "step_description": record.step_description,
                "watchdog_mask": record.watchdog_mask,
                "action_description": record.action_description,
                "normal_interlock_condition": record.normal_interlock_condition,
                "recovery_interlock_condition": record.recovery_interlock_condition,
                "recovery_interlock_action": record.recovery_interlock_action,
                "alarm_condition": record.alarm_condition,
                "skip_condition": record.skip_condition,
                "em_description_cn": record.em_description_cn,
                "op_sequence_under_em": record.op_sequence_under_em,
                "em_variable": record.em_variable,
                "created_at": record.created_at.isoformat()
                if record.created_at
                else None,
                "updated_at": record.updated_at.isoformat()
                if record.updated_at
                else None,
            }
            grouped[em_desc].append(record_dict)

        return grouped


# 创建CRUD实例
op_control = CRUDOPControlLogic(OPControlLogic)

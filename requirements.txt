# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   mcp
    #   sse-starlette
    #   starlette
    #   watchfiles
attrs==25.3.0
    # via
    #   cyclopts
    #   jsonschema
    #   referencing
authlib==1.6.0
    # via fastmcp
bcrypt==4.3.0
    # via passlib
certifi==2025.7.14
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via uvicorn
cryptography==45.0.5
    # via
    #   authlib
    #   python-jose
cyclopts==3.22.2
    # via fastmcp
dnspython==2.7.0
    # via email-validator
docstring-parser==0.16
    # via cyclopts
docutils==0.21.2
    # via rich-rst
ecdsa==0.19.1
    # via python-jose
email-validator==2.2.0
    # via pydantic
et-xmlfile==2.0.0
    # via openpyxl
exceptiongroup==1.3.0
    # via fastmcp
fastapi==0.116.1
    # via plc-auto-coding (pyproject.toml)
fastmcp==2.10.5
    # via plc-auto-coding (pyproject.toml)
greenlet==3.2.3
    # via sqlalchemy
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   fastmcp
    #   mcp
httpx-sse==0.4.1
    # via mcp
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
jsonschema==4.24.0
    # via mcp
jsonschema-specifications==2025.4.1
    # via jsonschema
markdown-it-py==3.0.0
    # via rich
mcp==1.11.0
    # via
    #   plc-auto-coding (pyproject.toml)
    #   fastmcp
mdurl==0.1.2
    # via markdown-it-py
numpy==2.3.1
    # via pandas
openapi-pydantic==0.5.1
    # via fastmcp
openpyxl==3.1.5
    # via plc-auto-coding (pyproject.toml)
pandas==2.3.1
    # via plc-auto-coding (pyproject.toml)
passlib==1.7.4
    # via plc-auto-coding (pyproject.toml)
pyasn1==0.6.1
    # via
    #   python-jose
    #   rsa
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   plc-auto-coding (pyproject.toml)
    #   fastapi
    #   fastmcp
    #   mcp
    #   openapi-pydantic
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via
    #   plc-auto-coding (pyproject.toml)
    #   mcp
pygments==2.19.2
    # via rich
pymysql==1.1.1
    # via plc-auto-coding (pyproject.toml)
pyperclip==1.9.0
    # via fastmcp
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.1.1
    # via
    #   plc-auto-coding (pyproject.toml)
    #   fastmcp
    #   pydantic-settings
    #   uvicorn
python-jose==3.5.0
    # via plc-auto-coding (pyproject.toml)
python-multipart==0.0.20
    # via
    #   plc-auto-coding (pyproject.toml)
    #   mcp
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via uvicorn
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.4
    # via plc-auto-coding (pyproject.toml)
rich==14.0.0
    # via
    #   cyclopts
    #   fastmcp
    #   rich-rst
rich-rst==1.3.1
    # via cyclopts
rpds-py==0.26.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via python-jose
six==1.17.0
    # via
    #   ecdsa
    #   python-dateutil
sniffio==1.3.1
    # via anyio
sqlalchemy==2.0.41
    # via plc-auto-coding (pyproject.toml)
sse-starlette==2.4.1
    # via
    #   plc-auto-coding (pyproject.toml)
    #   mcp
starlette==0.47.1
    # via
    #   fastapi
    #   mcp
typing-extensions==4.14.1
    # via
    #   anyio
    #   exceptiongroup
    #   fastapi
    #   pydantic
    #   pydantic-core
    #   referencing
    #   sqlalchemy
    #   starlette
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.5.0
    # via requests
uvicorn==0.35.0
    # via
    #   plc-auto-coding (pyproject.toml)
    #   mcp
uvloop==0.21.0
    # via uvicorn
watchfiles==1.1.0
    # via uvicorn
websockets==15.0.1
    # via uvicorn

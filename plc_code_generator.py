#!/usr/bin/env python3
"""
PLC代码生成器主入口脚本
使用重构后的代码生成服务
"""

import sys
import os

# 添加backend路径以便导入服务
sys.path.append(os.path.join(os.path.dirname(__file__), "backend"))

from backend.app.services.tp_code_generator import TPCodeGeneratorService
from backend.app.services.op_code_generator import OPCodeGeneratorService


def load_sample_data():
    """加载示例数据用于测试"""

    # 示例TP数据
    tp_data = [
        {
            "id": 1,
            "step_id": 0,
            "step_description": "等待启动信号",
            "motion_condition": "TRUE",
            "motion_action": "FALSE",
            "skip_condition": "FALSE",
            "skip_step": "FALSE",
            "tp_sequence": "io_stUm.TP[1]",
            "em_description_cn": "EM911进电芯",
            "em_variable": "io_stEmBtryIn",
            "op_sequence_under_em": "OP[1]",
        },
        {
            "id": 2,
            "step_id": 10,
            "step_description": "动作准备",
            "motion_condition": "TRUE",
            "motion_action": "bCmdAction[1]",
            "skip_condition": "bDoneAction[1]",
            "skip_step": "20",
            "tp_sequence": "io_stUm.TP[1]",
            "em_description_cn": "EM911进电芯",
            "em_variable": "io_stEmBtryIn",
            "op_sequence_under_em": "OP[1]",
        },
        {
            "id": 3,
            "step_id": 20,
            "step_description": "无电芯空运行",
            "motion_condition": "TRUE",
            "motion_action": "bCmdAction[2]",
            "skip_condition": "bDoneAction[2]",
            "skip_step": "500",
            "tp_sequence": "io_stUm.TP[1]",
            "em_description_cn": "EM911进电芯",
            "em_variable": "io_stEmBtryIn",
            "op_sequence_under_em": "OP[1]",
        },
        {
            "id": 4,
            "step_id": 500,
            "step_description": "结束",
            "motion_condition": "TRUE",
            "motion_action": "FALSE",
            "skip_condition": "FALSE",
            "skip_step": "FALSE",
            "tp_sequence": "io_stUm.TP[1]",
            "em_description_cn": "EM911进电芯",
            "em_variable": "io_stEmBtryIn",
            "op_sequence_under_em": "OP[1]",
        },
    ]

    # 示例OP数据
    op_data = [
        {
            "id": 1,
            "step_id": 10,
            "step_description": "动作描述",
            "watchdog_mask": "FALSE",
            "action_description": "SV1在位置1",
            "normal_interlock_condition": "SV1在位置1",
            "recovery_interlock_condition": "",
            "alarm_condition": "SV1超时",
            "skip_condition": "",
            "em_description_cn": "io_stEmBtryIn进电芯",
            "op_sequence_under_em": "OP[1]",
            "em_variable": "io_stEmBtryIn",
            "recovery_interlock_action": "",
        },
        {
            "id": 2,
            "step_id": 20,
            "step_description": "检查电芯",
            "watchdog_mask": "FALSE",
            "action_description": "YV1打开",
            "normal_interlock_condition": "YV1打开",
            "recovery_interlock_condition": "",
            "alarm_condition": "YV1超时",
            "skip_condition": "",
            "em_description_cn": "io_stEmBtryIn进电芯",
            "op_sequence_under_em": "OP[1]",
            "em_variable": "io_stEmBtryIn",
            "recovery_interlock_action": "",
        },
    ]

    # 示例执行器映射数据
    actuator_data = [
        {
            "parent_struct": "Y",
            "actuator": "Y",
            "command_variable": "Ctrl.arbAToPos[1]",
            "command_description_cn": "SV1在位置1",
        },
        {
            "parent_struct": "Y",
            "actuator": "Y",
            "command_variable": "Ctrl.arbAToPos[2]",
            "command_description_cn": "SV1在位置2",
        },
        {
            "parent_struct": "Y",
            "actuator": "Y",
            "command_variable": "Ctrl.bOpenExe",
            "command_description_cn": "YV1打开",
        },
        {
            "parent_struct": "Y",
            "actuator": "Y",
            "command_variable": "Ctrl.bCloseExe",
            "command_description_cn": "YV1关闭",
        },
        {
            "parent_struct": "Y",
            "actuator": "N",
            "command_variable": "IO.bDI_snCellCheck",
            "command_description_cn": "OK电芯",
        },
    ]

    return tp_data, op_data, actuator_data


def generate_tp_code_demo():
    """演示TP代码生成"""
    print("=" * 60)
    print("TP代码生成演示")
    print("=" * 60)

    # 加载示例数据
    tp_data, _, actuator_data = load_sample_data()

    # 创建生成器
    generator = TPCodeGeneratorService()

    # 生成代码
    try:
        code = generator.generate_all_tp_code(tp_data, actuator_data)
        print(code)

        # 检查未映射条件
        if generator.unmapped_conditions:
            print("\n" + "=" * 60)
            print("未映射的条件:")
            for condition in sorted(generator.unmapped_conditions):
                print(f"  - {condition}")

            print("\n条件映射模板:")
            print(generator.get_condition_mapping_template())
        else:
            print("\n✅ 所有条件都已正确映射")

    except Exception as e:
        print(f"❌ 代码生成失败: {e}")


def generate_op_code_demo():
    """演示OP代码生成"""
    print("=" * 60)
    print("OP代码生成演示")
    print("=" * 60)

    # 加载示例数据
    _, op_data, actuator_data = load_sample_data()

    # 创建生成器
    generator = OPCodeGeneratorService()

    # 生成代码
    try:
        code = generator.generate_all_op_code(op_data, actuator_data)
        print(code)

        # 检查未映射条件
        if generator.unmapped_conditions:
            print("\n" + "=" * 60)
            print("未映射的条件:")
            for condition in sorted(generator.unmapped_conditions):
                print(f"  - {condition}")

            print("\n条件映射模板:")
            print(generator.get_condition_mapping_template())
        else:
            print("\n✅ 所有条件都已正确映射")

    except Exception as e:
        print(f"❌ 代码生成失败: {e}")


def main():
    """主函数"""
    print("🚀 PLC代码生成器")
    print("使用重构后的代码生成服务")
    print()

    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "tp":
            generate_tp_code_demo()
        elif command == "op":
            generate_op_code_demo()
        elif command == "both":
            generate_tp_code_demo()
            print("\n" + "=" * 80 + "\n")
            generate_op_code_demo()
        else:
            print(f"❌ 未知命令: {command}")
            print("可用命令: tp, op, both")
    else:
        print("用法:")
        print("  python plc_code_generator.py tp    # 生成TP代码")
        print("  python plc_code_generator.py op    # 生成OP代码")
        print("  python plc_code_generator.py both  # 生成TP和OP代码")
        print()
        print("默认生成两种代码:")
        generate_tp_code_demo()
        print("\n" + "=" * 80 + "\n")
        generate_op_code_demo()


if __name__ == "__main__":
    main()

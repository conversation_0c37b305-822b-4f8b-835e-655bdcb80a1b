# PLC Auto Coding System - 前端管理界面

基于Vue 3 + TypeScript + Element Plus的现代化管理界面，用于维护PLC自动编码系统的数据库表和生成代码。

## 功能特性

- **现代化界面**: 使用Element Plus组件库，提供美观易用的用户界面
- **数据管理**: 支持TP控制逻辑、OP控制逻辑、执行器变量映射三张表的完整CRUD操作
- **智能搜索**: 支持多条件筛选、分页查询、实时搜索
- **代码生成**: 集成代码生成功能，支持在线预览和文件下载
- **条件验证**: 实时验证条件映射完整性，提供详细的验证报告
- **响应式设计**: 适配不同屏幕尺寸，支持移动端访问

## 技术栈

- **Vue 3**: 现代化前端框架，使用Composition API
- **TypeScript**: 类型安全的JavaScript
- **Element Plus**: 企业级Vue组件库
- **Pinia**: Vue官方状态管理库
- **Axios**: HTTP客户端
- **Monaco Editor**: VS Code编辑器组件
- **Vue Router**: Vue官方路由管理
- **Vite**: 快速构建工具

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/        # 通用组件
│   │   └── Layout/       # 布局组件
│   ├── pages/            # 页面组件
│   ├── services/         # API服务
│   ├── stores/           # 状态管理
│   ├── types/            # TypeScript类型定义
│   ├── App.tsx           # 主应用组件
│   └── index.tsx         # 应用入口
├── package.json          # 依赖配置
└── README.md            # 说明文档
```

## 安装和运行

### 1. 安装依赖

```bash
cd frontend
npm install
```

### 2. 启动开发服务器

```bash
npm start
```

应用将在 http://localhost:3000 启动

### 3. 构建生产版本

```bash
npm run build
```

构建文件将输出到 `build/` 目录

## 页面功能

### 仪表板 (/)
- 系统数据统计概览
- 条件映射验证状态
- 快速操作入口

### TP控制逻辑管理 (/tp-control)
- TP数据的增删改查
- 支持按步骤ID、EM描述、EM变量筛选
- 批量操作和数据导入导出

### OP控制逻辑管理 (/op-control)
- OP数据的增删改查
- 支持多种筛选条件
- 详细的字段编辑功能

### 执行器变量映射管理 (/actuator-map)
- 映射关系的维护
- 支持关键字搜索
- 字段说明和使用指南

### 代码生成 (/code-generation)
- TP和OP代码在线生成
- 代码实时预览和语法高亮
- 条件映射验证
- 代码文件下载

## API集成

前端通过Axios与后端API进行通信，所有API调用都在 `src/services/api.ts` 中定义：

- **tpControlApi**: TP控制逻辑相关API
- **opControlApi**: OP控制逻辑相关API
- **actuatorMapApi**: 执行器变量映射相关API
- **codeGenerationApi**: 代码生成相关API

## 状态管理

使用Zustand进行全局状态管理，主要状态包括：

- 全局加载状态
- 当前选中菜单
- 侧边栏折叠状态
- 主题设置

## 开发说明

### 添加新页面

1. 在 `src/pages/` 中创建新的页面组件
2. 在 `src/App.tsx` 中添加路由配置
3. 在 `src/components/Layout/AppSider.tsx` 中添加菜单项

### 添加新的API

1. 在 `src/types/index.ts` 中定义相关类型
2. 在 `src/services/api.ts` 中添加API方法
3. 在页面组件中调用API方法

### 样式定制

- 全局样式在 `src/index.css` 中定义
- 组件样式使用Ant Design的主题定制功能
- 支持响应式设计，适配不同屏幕尺寸

## 部署说明

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm run build
npm run preview
```

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

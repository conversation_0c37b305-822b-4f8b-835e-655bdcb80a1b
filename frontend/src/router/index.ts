import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表板' }
      },
      {
        path: '/tp-control',
        name: 'TPControl',
        component: () => import('@/views/TPControl.vue'),
        meta: { title: 'TP控制逻辑' }
      },
      {
        path: '/op-control',
        name: 'OPControl',
        component: () => import('@/views/OPControl.vue'),
        meta: { title: 'OP控制逻辑' }
      },
      {
        path: '/actuator-map',
        name: 'ActuatorMap',
        component: () => import('@/views/ActuatorMap.vue'),
        meta: { title: '执行器变量映射' }
      },
      {
        path: '/code-generation',
        name: 'CodeGeneration',
        component: () => import('@/views/CodeGeneration.vue'),
        meta: { title: '代码生成' }
      },
      {
        path: '/flow-designer',
        name: 'FlowDesigner',
        component: () => import('@/views/FlowDesigner.vue'),
        meta: { title: '流程设计器' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router

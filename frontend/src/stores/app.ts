import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 全局加载状态
  const loading = ref(false)
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  // 当前选中的菜单
  const selectedMenu = ref('/')
  const setSelectedMenu = (menu: string) => {
    selectedMenu.value = menu
  }

  // 侧边栏折叠状态
  const collapsed = ref(false)
  const setCollapsed = (value: boolean) => {
    collapsed.value = value
  }

  // 主题设置
  const theme = ref<'light' | 'dark'>('light')
  const setTheme = (value: 'light' | 'dark') => {
    theme.value = value
  }

  return {
    loading,
    setLoading,
    selectedMenu,
    setSelectedMenu,
    collapsed,
    setCollapsed,
    theme,
    setTheme
  }
})

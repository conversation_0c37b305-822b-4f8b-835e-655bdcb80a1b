<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import { Position } from '@vue-flow/core'

interface NodeData {
  toolbarVisible?: boolean
  toolbarPosition?: Position
  name: string
  condition: string
  action: string
}

interface FlowNode {
  id: string
  type: string
  label: string
  data: NodeData
  position: { x: number; y: number }
  style?: Record<string, any>
}

interface FlowEdge {
  id: string
  source: string
  target: string
  markerEnd: any
  style?: Record<string, any>
  label?: string
  data?: {
    condition: string
  }
}

interface Props {
  selectedNode: FlowNode | null
  selectedEdge: FlowEdge | null
}

interface Emits {
  (e: 'update-node', nodeId: string, data: Partial<NodeData>): void
  (e: 'update-edge', edgeId: string, condition: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const formData = ref({
  name: '',
  condition: '',
  action: '',
  toolbarPosition: Position.Top,
  toolbarVisible: false
})

// 边表单数据
const edgeFormData = ref({
  condition: ''
})

// 工具栏位置选项
const toolbarPositions = [
  { label: '顶部', value: Position.Top },
  { label: '右侧', value: Position.Right },
  { label: '底部', value: Position.Bottom },
  { label: '左侧', value: Position.Left },
]

// 监听选中节点变化
watch(() => props.selectedNode, (newNode) => {
  if (newNode) {
    formData.value = {
      name: newNode.data.name || '',
      condition: newNode.data.condition || '',
      action: newNode.data.action || '',
      toolbarPosition: newNode.data.toolbarPosition || Position.Top,
      toolbarVisible: newNode.data.toolbarVisible || false
    }
  } else {
    // 清空表单
    formData.value = {
      name: '',
      condition: '',
      action: '',
      toolbarPosition: Position.Top,
      toolbarVisible: false
    }
  }
}, { immediate: true })

// 监听选中边变化
watch(() => props.selectedEdge, (newEdge) => {
  if (newEdge) {
    edgeFormData.value = {
      condition: newEdge.data?.condition || ''
    }
  } else {
    edgeFormData.value = {
      condition: ''
    }
  }
}, { immediate: true })

// 更新节点数据
const updateNode = () => {
  if (props.selectedNode) {
    emit('update-node', props.selectedNode.id, {
      name: formData.value.name,
      condition: formData.value.condition,
      action: formData.value.action,
      toolbarPosition: formData.value.toolbarPosition,
      toolbarVisible: formData.value.toolbarVisible
    })
  }
}

// 更新边数据
const updateEdge = () => {
  if (props.selectedEdge) {
    emit('update-edge', props.selectedEdge.id, edgeFormData.value.condition)
  }
}

// 实时更新（可选）
const enableRealTimeUpdate = ref(true)

watch(formData, () => {
  if (enableRealTimeUpdate.value && props.selectedNode) {
    updateNode()
  }
}, { deep: true })

watch(edgeFormData, () => {
  if (enableRealTimeUpdate.value && props.selectedEdge) {
    updateEdge()
  }
}, { deep: true })

// 重置表单
const resetForm = () => {
  if (props.selectedNode) {
    formData.value = {
      name: props.selectedNode.data.name || '',
      condition: props.selectedNode.data.condition || '',
      action: props.selectedNode.data.action || '',
      toolbarPosition: props.selectedNode.data.toolbarPosition || Position.Top,
      toolbarVisible: props.selectedNode.data.toolbarVisible || false
    }
  }
  if (props.selectedEdge) {
    edgeFormData.value = {
      condition: props.selectedEdge.data?.condition || ''
    }
  }
}

// 常用条件模板
const conditionTemplates = [
  'SV1在位置1',
  'SV2在位置2',
  'YV1在开位',
  'YV2在关位',
  'bDoneAction[1]',
  'bDoneAction[2]',
  'bCmdAction[1]',
  'bAlm[10]'
]

// 常用跳转条件模板
const jumpConditionTemplates = [
  '成功',
  '失败',
  '超时',
  '异常',
  '完成',
  '跳过',
  '重试',
  '继续'
]

// 常用动作模板
const actionTemplates = [
  '启动电机',
  '停止电机',
  '打开阀门',
  '关闭阀门',
  '检查状态',
  '发送信号',
  '等待完成',
  '报警处理'
]

// 插入模板
const insertTemplate = (field: 'condition' | 'action', template: string) => {
  if (formData.value[field]) {
    formData.value[field] += ';' + template
  } else {
    formData.value[field] = template
  }
}

// 插入跳转条件模板
const insertJumpConditionTemplate = (template: string) => {
  edgeFormData.value.condition = template
}
</script>

<template>
  <div class="properties-panel">
    <div class="panel-header">
      <h3>节点属性</h3>
      <div class="header-actions">
        <el-switch
          v-model="enableRealTimeUpdate"
          size="small"
          active-text="实时更新"
          inactive-text="手动更新"
        />
      </div>
    </div>

    <div v-if="!selectedNode && !selectedEdge" class="no-selection">
      <el-empty description="请选择一个节点或连接线来编辑属性" />
    </div>

    <!-- 节点编辑 -->
    <div v-else-if="selectedNode" class="panel-content">
      <el-form :model="formData" label-width="80px" size="small">
        <!-- 基本信息 -->
        <el-divider content-position="left">基本信息</el-divider>

        <el-form-item label="节点名称">
          <el-input
            v-model="formData.name"
            placeholder="请输入节点名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="条件">
          <el-input
            v-model="formData.condition"
            type="textarea"
            :rows="3"
            placeholder="请输入执行条件，多个条件用分号分隔"
            clearable
          />
          <div class="template-buttons">
            <span class="template-label">常用条件:</span>
            <el-button
              v-for="template in conditionTemplates"
              :key="template"
              size="small"
              text
              @click="insertTemplate('condition', template)"
            >
              {{ template }}
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="动作">
          <el-input
            v-model="formData.action"
            type="textarea"
            :rows="3"
            placeholder="请输入执行动作"
            clearable
          />
          <div class="template-buttons">
            <span class="template-label">常用动作:</span>
            <el-button
              v-for="template in actionTemplates"
              :key="template"
              size="small"
              text
              @click="insertTemplate('action', template)"
            >
              {{ template }}
            </el-button>
          </div>
        </el-form-item>

        <!-- 显示设置 -->
        <el-divider content-position="left">显示设置</el-divider>

        <el-form-item label="工具栏位置">
          <el-select v-model="formData.toolbarPosition" placeholder="选择工具栏位置">
            <el-option
              v-for="option in toolbarPositions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="工具栏">
          <el-switch
            v-model="formData.toolbarVisible"
            active-text="始终显示"
            inactive-text="悬停显示"
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <div class="panel-actions">
          <el-button
            v-if="!enableRealTimeUpdate"
            type="primary"
            @click="updateNode"
          >
            应用更改
          </el-button>
          <el-button @click="resetForm">
            重置
          </el-button>
        </div>
      </el-form>

      <!-- 节点信息 -->
      <el-divider content-position="left">节点信息</el-divider>
      <div class="node-info">
        <p><strong>节点ID:</strong> {{ selectedNode.id }}</p>
        <p><strong>节点类型:</strong> {{ selectedNode.type }}</p>
        <p><strong>位置:</strong> ({{ selectedNode.position.x }}, {{ selectedNode.position.y }})</p>
      </div>
    </div>

    <!-- 边编辑 -->
    <div v-else-if="selectedEdge" class="panel-content">
      <el-form :model="edgeFormData" label-width="80px" size="small">
        <!-- 基本信息 -->
        <el-divider content-position="left">连接线属性</el-divider>

        <el-form-item label="跳转条件">
          <el-input
            v-model="edgeFormData.condition"
            placeholder="请输入跳转条件，留空表示无条件跳转"
            clearable
          />
          <div class="template-buttons">
            <span class="template-label">常用条件:</span>
            <el-button
              v-for="template in jumpConditionTemplates"
              :key="template"
              size="small"
              text
              @click="insertJumpConditionTemplate(template)"
            >
              {{ template }}
            </el-button>
          </div>
        </el-form-item>

        <!-- 操作按钮 -->
        <div class="panel-actions">
          <el-button
            v-if="!enableRealTimeUpdate"
            type="primary"
            @click="updateEdge"
          >
            应用更改
          </el-button>
          <el-button @click="resetForm">
            重置
          </el-button>
        </div>
      </el-form>

      <!-- 连接线信息 -->
      <el-divider content-position="left">连接线信息</el-divider>
      <div class="node-info">
        <p><strong>连接线ID:</strong> {{ selectedEdge.id }}</p>
        <p><strong>源节点:</strong> {{ selectedEdge.source }}</p>
        <p><strong>目标节点:</strong> {{ selectedEdge.target }}</p>
        <p><strong>当前条件:</strong> {{ selectedEdge.data?.condition || '无条件' }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.properties-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
}

.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.template-buttons {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.template-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.panel-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
}

.node-info {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
}

.node-info p {
  margin: 4px 0;
  color: #606266;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>

<script lang="ts" setup>
import { Handle, Position } from '@vue-flow/core'
import { NodeToolbar } from '@vue-flow/node-toolbar'

interface NodeData {
  toolbarVisible?: boolean
  toolbarPosition?: Position
  name: string
  condition: string
  action: string
}

interface Props {
  data: NodeData
  label: string
  selected?: boolean
  id?: string
}

interface Emits {
  (e: 'delete', nodeId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 处理删除按钮点击
const handleDelete = () => {
  if (props.id) {
    emit('delete', props.id)
  }
}

// 处理复制按钮点击
const handleCopy = () => {
  console.log('复制节点:', props.data)
}

// 处理展开按钮点击
const handleExpand = () => {
  console.log('展开节点:', props.data)
}
</script>

<template>
  <div
    class="custom-node"
    :class="{ 'selected': selected }"
  >
    <NodeToolbar
      :is-visible="data.toolbarVisible || selected"
      :position="data.toolbarPosition || Position.Top"
    >
      <div class="node-toolbar">
        <el-button size="small" type="danger" @click="handleDelete">
          <el-icon><Delete /></el-icon>
        </el-button>
        <el-button size="small" @click="handleCopy">
          <el-icon><CopyDocument /></el-icon>
        </el-button>
        <el-button size="small" @click="handleExpand">
          <el-icon><Expand /></el-icon>
        </el-button>
      </div>
    </NodeToolbar>

    <div class="node-content">
      <!-- 节点标题 -->
      <div class="node-title">
        {{ data.name || label }}
      </div>

      <!-- 节点详情 -->
      <div class="node-details">
        <div v-if="data.condition" class="node-condition">
          <span class="label">条件:</span>
          <span class="value">{{ data.condition }}</span>
        </div>
        <div v-if="data.action" class="node-action">
          <span class="label">动作:</span>
          <span class="value">{{ data.action }}</span>
        </div>
      </div>
    </div>

    <!-- 连接点 - 纵向布局 -->
    <Handle type="target" :position="Position.Top" class="handle-target" />
    <Handle type="source" :position="Position.Bottom" class="handle-source" />
  </div>
</template>

<style scoped>
.custom-node {
  background: white;
  border: 2px solid #409eff;
  border-radius: 8px;
  width: 160px;
  min-height: 100px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  flex-direction: column;
}

.custom-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.custom-node.selected {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.node-toolbar {
  display: flex;
  gap: 4px;
  background: white;
  padding: 4px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.node-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  text-align: center;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 6px;
}

.node-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.node-condition,
.node-action {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.label {
  font-weight: 500;
  color: #606266;
}

.value {
  color: #303133;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  word-break: break-all;
  font-family: 'Courier New', monospace;
}

.handle-target,
.handle-source {
  width: 10px;
  height: 10px;
  background: #409eff;
  border: 2px solid white;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.handle-target:hover,
.handle-source:hover {
  background: #67c23a;
  transform: scale(1.2);
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.3);
}

/* 连接点位置调整 */
.handle-target {
  top: -5px;
}

.handle-source {
  bottom: -5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-node {
    min-width: 120px;
    padding: 8px;
  }

  .node-title {
    font-size: 12px;
  }

  .node-details {
    font-size: 10px;
  }
}
</style>

<template>
  <div class="data-table-container" ref="containerRef">
    <el-table
      ref="tableRef"
      :data="data"
      v-loading="loading"
      stripe
      border
      :table-layout="tableLayout"
      :scrollbar-always-on="true"
      :fit="false"
      :flexible="false"
      :height="height"
      :max-height="maxHeight"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      v-bind="$attrs"
    >
      <slot />
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted, watch } from 'vue'

interface Props {
  data: any[]
  loading?: boolean
  tableLayout?: 'fixed' | 'auto'
  height?: string | number
  maxHeight?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  tableLayout: 'auto'
})

const emit = defineEmits<{
  selectionChange: [selection: any[]]
}>()

const tableRef = ref()
const containerRef = ref()

const handleSelectionChange = (selection: any[]) => {
  emit('selectionChange', selection)
}

// 计算空白区域
const calculateEmptySpace = () => {
  nextTick(() => {
    if (!containerRef.value || !tableRef.value) return

    const container = containerRef.value
    const table = tableRef.value.$el
    const tableWrapper = table.querySelector('.el-table__body-wrapper')
    const tableBody = table.querySelector('.el-table__body')

    if (!tableWrapper || !tableBody) return

    const containerWidth = container.offsetWidth
    const tableWidth = tableBody.scrollWidth || tableBody.offsetWidth

    // 表格左对齐，只计算右侧空白区域
    const rightEmptySpace = Math.max(0, containerWidth - tableWidth)

    // 清除左侧空白样式（因为我们强制左对齐）
    container.classList.remove('has-left-empty-space')
    container.style.removeProperty('--left-empty-space-width')

    // 右侧空白
    if (rightEmptySpace > 5) { // 5px 阈值避免误判
      container.classList.add('has-empty-space')
      container.style.setProperty('--empty-space-width', `${rightEmptySpace}px`)
    } else {
      container.classList.remove('has-empty-space')
      container.style.removeProperty('--empty-space-width')
    }
  })
}

// 表格重新计算布局
const doLayout = () => {
  nextTick(() => {
    if (tableRef.value) {
      tableRef.value.doLayout()
      // 布局完成后计算空白区域
      setTimeout(calculateEmptySpace, 100)
    }
  })
}

// 监听数据变化，重新计算布局
watch(() => props.data, () => {
  doLayout()
}, { deep: true })

// 监听窗口大小变化
const handleResize = () => {
  doLayout()
}

onMounted(() => {
  doLayout()
  window.addEventListener('resize', handleResize)
})

// 清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

defineExpose({
  doLayout,
  tableRef
})
</script>

<style scoped>
.data-table-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 确保表格占满容器，去除留白 */
.data-table-container :deep(.el-table) {
  width: 100% !important;
  height: 100% !important;
  table-layout: auto !important;
}

/* 强制表格左对齐 */
.data-table-container :deep(.el-table) {
  margin: 0 !important;
  text-align: left;
}

.data-table-container :deep(.el-table__body-wrapper),
.data-table-container :deep(.el-table__header-wrapper) {
  text-align: left;
}

/* 当表格内容宽度小于容器时，确保表格贴左显示 */
.data-table-container :deep(.el-table__body),
.data-table-container :deep(.el-table__header) {
  width: auto !important;
  min-width: fit-content;
  margin: 0 !important;
}

/* 确保表格不会居中 */
.data-table-container :deep(.el-table__body-wrapper .el-table__body),
.data-table-container :deep(.el-table__header-wrapper .el-table__header) {
  margin-left: 0 !important;
  margin-right: auto !important;
}

/* 优化表格布局，防止表头错位 */
.data-table-container :deep(.el-table__header-wrapper) {
  overflow: hidden;
}

.data-table-container :deep(.el-table__body-wrapper) {
  overflow: auto !important;
  flex: 1;
}

/* 修复固定列的对齐问题 */
.data-table-container :deep(.el-table__fixed),
.data-table-container :deep(.el-table__fixed-right) {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
}

/* 表格行高固定，防止换行 */
.data-table-container :deep(.el-table .el-table__row) {
  height: 50px !important;
}

.data-table-container :deep(.el-table .el-table__cell) {
  padding: 8px 12px;
  vertical-align: middle;
  height: 50px !important;
  line-height: 34px;
}

/* 只对有 show-overflow-tooltip 的列应用省略号 */
.data-table-container :deep(.el-table .el-table__cell.is-tooltip) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 表头样式优化，防止换行 */
.data-table-container :deep(.el-table__header .el-table__cell) {
  background-color: var(--el-fill-color-light);
  font-weight: 600;
  color: var(--el-text-color-primary);
  height: 50px !important;
  line-height: 34px;
  white-space: nowrap;
}



/* 确保表格列均匀分布，去除留白 */
.data-table-container :deep(.el-table colgroup col:not([name="gutter"])) {
  width: auto !important;
}

/* 确保表格不会产生水平滚动条问题 */
.data-table-container :deep(.el-table__body) {
  width: 100% !important;
}

/* 滚动条样式优化 */
.data-table-container :deep(.el-scrollbar__bar) {
  opacity: 0.6;
}

.data-table-container :deep(.el-scrollbar__bar:hover) {
  opacity: 1;
}

/* 修复表头与数据行对齐问题 */
.data-table-container :deep(.el-table__header) {
  width: 100% !important;
}

.data-table-container :deep(.el-table__header-wrapper .el-table__header) {
  width: 100% !important;
}

/* 确保固定列高度一致 */
.data-table-container :deep(.el-table__fixed .el-table__cell),
.data-table-container :deep(.el-table__fixed-right .el-table__cell) {
  height: 50px !important;
  line-height: 34px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 右侧空白区域样式 */
.data-table-container::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(90deg,
    var(--el-border-color) 0px,
    var(--el-border-color) 1px,
    var(--el-fill-color-lighter) 1px,
    var(--el-fill-color-lighter) 100%
  );
  box-shadow: inset 2px 0 8px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  z-index: 0;
  transition: width 0.3s ease;
}

/* 当表格宽度小于容器时显示右侧空白区域 */
.data-table-container.has-empty-space::after {
  width: var(--empty-space-width, 0);
}

/* 左侧空白区域样式 */
.data-table-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(270deg,
    var(--el-border-color) 0px,
    var(--el-border-color) 1px,
    var(--el-fill-color-lighter) 1px,
    var(--el-fill-color-lighter) 100%
  );
  box-shadow: inset -2px 0 8px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  z-index: 0;
  transition: width 0.3s ease;
}

/* 当有左侧偏移时显示左侧空白区域 */
.data-table-container.has-left-empty-space::before {
  width: var(--left-empty-space-width, 0);
}

/* 确保表格内容在空白区域之上 */
.data-table-container :deep(.el-table) {
  position: relative;
  z-index: 1;
}
</style>

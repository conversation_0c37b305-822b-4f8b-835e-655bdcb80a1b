<script lang="ts" setup>
import { ref, computed } from 'vue'
import { Position, VueFlow, useVueFlow, MarkerType } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import { Plus, Delete, Close, Sort, Upload, Download, DataBoard } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { tpControlApi } from '@/services/api'
import CustomNode from './CustomNode.vue'
import PropertiesPanel from './PropertiesPanel.vue'
import LoadTPFlowDialog from './LoadTPFlowDialog.vue'
import SaveTPFlowDialog from './SaveTPFlowDialog.vue'

// 节点数据接口
interface NodeData {
  toolbarVisible?: boolean
  toolbarPosition?: Position
  name: string
  condition: string
  action: string
  tpId?: number  // TP记录的ID，用于更新操作
}

// 节点类型
interface FlowNode {
  id: string
  type: string
  label: string
  data: NodeData
  position: { x: number; y: number }
  style?: Record<string, any>
}

// 边数据接口
interface FlowEdge {
  id: string
  source: string
  target: string
  markerEnd: any
  style?: Record<string, any>
  label?: string
  data?: {
    condition: string
  }
}

const defaultNodeStyle = {
  border: '2px solid #409eff',
  background: 'white',
  borderRadius: '8px',
  minWidth: '150px',
  minHeight: '80px',
}

// 初始节点数据 - 纵向布局
const elements = ref<FlowNode[]>([
  {
    id: '1',
    type: 'custom',
    label: '开始节点',
    data: {
      toolbarPosition: Position.Top,
      name: '开始节点',
      condition: '',
      action: '初始化系统'
    },
    position: { x: 300, y: 50 },
    style: { ...defaultNodeStyle, border: '2px solid #67c23a' },
  },
  {
    id: '2',
    type: 'custom',
    label: '条件判断',
    data: {
      toolbarPosition: Position.Right,
      name: '条件判断',
      condition: 'SV1在位置1',
      action: '检查阀门状态'
    },
    position: { x: 300, y: 200 },
    style: { ...defaultNodeStyle, border: '2px solid #e6a23c' },
  },
  {
    id: '3',
    type: 'custom',
    label: '执行动作',
    data: {
      toolbarPosition: Position.Bottom,
      name: '执行动作',
      condition: 'bDoneAction[1]',
      action: '启动电机'
    },
    position: { x: 300, y: 350 },
    style: defaultNodeStyle,
  },
])

// 连接线数据 - 添加箭头标记和条件
const edges = ref<FlowEdge[]>([
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    markerEnd: MarkerType.ArrowClosed,
    style: { strokeWidth: 2, stroke: '#409eff' },
    label: '',
    data: { condition: '' }
  },
  {
    id: 'e2-3',
    source: '2',
    target: '3',
    markerEnd: MarkerType.ArrowClosed,
    style: { strokeWidth: 2, stroke: '#409eff' },
    label: '成功',
    data: { condition: '成功' }
  },
])

// 当前选中的节点和边
const selectedNode = ref<FlowNode | null>(null)
const selectedEdge = ref<FlowEdge | null>(null)

// 对话框状态
const loadDialogVisible = ref(false)
const saveDialogVisible = ref(false)

// 当前流程的全局信息
const currentFlowInfo = ref<{
  emDescription: string
  emVariable: string
  opSequence: string
  tpSequence: string
} | null>(null)

// Vue Flow 实例
const { onNodeClick, onConnect, onEdgeClick, addNodes, updateNode, addEdges } = useVueFlow()

// 处理节点点击事件
onNodeClick((event) => {
  const node = elements.value.find(n => n.id === event.node.id)
  if (node) {
    selectedNode.value = node
    selectedEdge.value = null // 清空边选择
  }
})

// 处理边点击事件
onEdgeClick((event) => {
  const edge = edges.value.find(e => e.id === event.edge.id)
  if (edge) {
    selectedEdge.value = edge
    selectedNode.value = null // 清空节点选择
  }
})

// 处理连接事件
onConnect((connection) => {
  const newEdge: FlowEdge = {
    id: `e${connection.source}-${connection.target}`,
    source: connection.source!,
    target: connection.target!,
    markerEnd: MarkerType.ArrowClosed,
    style: { strokeWidth: 2, stroke: '#409eff' },
    label: '',
    data: { condition: '' }
  }
  edges.value.push(newEdge)
})

// 更新节点属性
const updateNodeData = (nodeId: string, newData: Partial<NodeData>) => {
  const nodeIndex = elements.value.findIndex(n => n.id === nodeId)
  if (nodeIndex !== -1) {
    elements.value[nodeIndex].data = { ...elements.value[nodeIndex].data, ...newData }
    elements.value[nodeIndex].label = newData.name || elements.value[nodeIndex].label
  }
}

// 更新边属性
const updateEdgeData = (edgeId: string, condition: string) => {
  const edgeIndex = edges.value.findIndex(e => e.id === edgeId)
  if (edgeIndex !== -1) {
    edges.value[edgeIndex].data = { condition }
    edges.value[edgeIndex].label = condition
  }
}

// 添加新节点 - 智能位置计算
const addNewNode = () => {
  const newId = (elements.value.length + 1).toString()

  // 计算新节点位置
  let newX = 300
  let newY = 80

  if (elements.value.length > 0) {
    // 找到最底部的节点
    const maxY = Math.max(...elements.value.map(node => node.position.y))
    newY = maxY + 180 // 在最底部节点下方

    // 如果有选中的节点，优先在其下方添加
    if (selectedNode.value) {
      newY = selectedNode.value.position.y + 180
      newX = selectedNode.value.position.x
    }
  }

  const newNode: FlowNode = {
    id: newId,
    type: 'custom',
    label: '新节点',
    data: {
      toolbarPosition: Position.Top,
      name: '新节点',
      condition: '',
      action: ''
    },
    position: { x: newX, y: newY },
    style: defaultNodeStyle,
  }

  elements.value.push(newNode)

  // 智能连接逻辑
  if (selectedNode.value) {
    // 如果有选中节点，连接到选中节点
    const newEdge: FlowEdge = {
      id: `e${selectedNode.value.id}-${newId}`,
      source: selectedNode.value.id,
      target: newId,
      markerEnd: MarkerType.ArrowClosed,
      style: { strokeWidth: 2, stroke: '#409eff' },
      label: '',
      data: { condition: '' }
    }
    edges.value.push(newEdge)
  } else if (elements.value.length > 1) {
    // 否则连接到最后一个节点
    const lastNode = elements.value[elements.value.length - 2] // -2 因为新节点已经添加
    const newEdge: FlowEdge = {
      id: `e${lastNode.id}-${newId}`,
      source: lastNode.id,
      target: newId,
      markerEnd: MarkerType.ArrowClosed,
      style: { strokeWidth: 2, stroke: '#409eff' },
      label: '',
      data: { condition: '' }
    }
    edges.value.push(newEdge)
  }

  // 自动选中新节点
  selectedNode.value = newNode
}

// 删除节点
const deleteNode = (nodeId: string) => {
  elements.value = elements.value.filter(n => n.id !== nodeId)
  edges.value = edges.value.filter(e => e.source !== nodeId && e.target !== nodeId)
  if (selectedNode.value?.id === nodeId) {
    selectedNode.value = null
  }
}

// 删除边
const deleteEdge = (edgeId: string) => {
  edges.value = edges.value.filter(e => e.id !== edgeId)
  if (selectedEdge.value?.id === edgeId) {
    selectedEdge.value = null
  }
}

// 清空选择
const clearSelection = () => {
  selectedNode.value = null
  selectedEdge.value = null
}

// 自动排列节点 - 智能布局
const autoLayout = () => {
  if (elements.value.length === 0) return

  // 构建节点关系图
  const nodeMap = new Map<string, FlowNode>()
  const outgoingEdges = new Map<string, FlowEdge[]>()
  const incomingEdges = new Map<string, FlowEdge[]>()

  // 初始化映射
  elements.value.forEach(node => {
    nodeMap.set(node.id, node)
    outgoingEdges.set(node.id, [])
    incomingEdges.set(node.id, [])
  })

  // 构建边关系
  edges.value.forEach(edge => {
    outgoingEdges.get(edge.source)?.push(edge)
    incomingEdges.get(edge.target)?.push(edge)
  })

  // 找到起始节点（没有入边的节点）
  const startNodes = elements.value.filter(node =>
    incomingEdges.get(node.id)?.length === 0
  )

  if (startNodes.length === 0) {
    // 如果没有明确的起始节点，使用第一个节点
    startNodes.push(elements.value[0])
  }

  // 布局参数
  const nodeWidth = 180
  const nodeHeight = 120
  const horizontalSpacing = 220  // 水平间距
  const verticalSpacing = 180    // 垂直间距
  const startX = 100
  const startY = 80

  // 已处理的节点
  const processedNodes = new Set<string>()
  const nodePositions = new Map<string, { x: number; y: number }>()

  // 层级布局
  const layoutLevel = (currentNodes: FlowNode[], level: number) => {
    if (currentNodes.length === 0) return

    // 计算当前层级的Y坐标
    const currentY = startY + level * verticalSpacing

    // 如果只有一个节点，居中放置
    if (currentNodes.length === 1) {
      const node = currentNodes[0]
      const x = startX + 2 * horizontalSpacing // 居中位置
      nodePositions.set(node.id, { x, y: currentY })
      processedNodes.add(node.id)
    } else {
      // 多个节点横向排列
      const totalWidth = (currentNodes.length - 1) * horizontalSpacing
      const startXForLevel = startX + horizontalSpacing - totalWidth / 2

      currentNodes.forEach((node, index) => {
        const x = startXForLevel + index * horizontalSpacing
        nodePositions.set(node.id, { x, y: currentY })
        processedNodes.add(node.id)
      })
    }

    // 收集下一层级的节点
    const nextLevelNodes = new Set<FlowNode>()

    currentNodes.forEach(node => {
      const outgoing = outgoingEdges.get(node.id) || []
      outgoing.forEach(edge => {
        const targetNode = nodeMap.get(edge.target)
        if (targetNode && !processedNodes.has(targetNode.id)) {
          // 检查目标节点的所有入边是否都已处理
          const incoming = incomingEdges.get(targetNode.id) || []
          const allIncomingProcessed = incoming.every(inEdge =>
            processedNodes.has(inEdge.source)
          )

          if (allIncomingProcessed) {
            nextLevelNodes.add(targetNode)
          }
        }
      })
    })

    // 递归处理下一层级
    if (nextLevelNodes.size > 0) {
      layoutLevel(Array.from(nextLevelNodes), level + 1)
    }
  }

  // 开始布局
  layoutLevel(startNodes, 0)

  // 处理剩余未处理的节点（可能存在循环引用）
  const remainingNodes = elements.value.filter(node => !processedNodes.has(node.id))
  if (remainingNodes.length > 0) {
    const maxLevel = Math.max(...Array.from(nodePositions.values()).map(pos => pos.y))
    const nextLevel = Math.floor((maxLevel - startY) / verticalSpacing) + 1
    layoutLevel(remainingNodes, nextLevel)
  }

  // 应用位置
  elements.value.forEach(node => {
    const position = nodePositions.get(node.id)
    if (position) {
      node.position = position
    }
  })

  // 优化重叠节点
  optimizeOverlappingNodes()
}

// 优化重叠节点位置
const optimizeOverlappingNodes = () => {
  const nodeWidth = 180
  const nodeHeight = 120
  const minSpacing = 20

  // 按Y坐标分组
  const levelGroups = new Map<number, FlowNode[]>()

  elements.value.forEach(node => {
    const y = Math.round(node.position.y / 10) * 10 // 四舍五入到最近的10
    if (!levelGroups.has(y)) {
      levelGroups.set(y, [])
    }
    levelGroups.get(y)!.push(node)
  })

  // 对每个层级的节点进行水平位置优化
  levelGroups.forEach((nodes, y) => {
    if (nodes.length <= 1) return

    // 按X坐标排序
    nodes.sort((a, b) => a.position.x - b.position.x)

    // 检查并调整重叠
    for (let i = 1; i < nodes.length; i++) {
      const prevNode = nodes[i - 1]
      const currentNode = nodes[i]

      const minX = prevNode.position.x + nodeWidth + minSpacing
      if (currentNode.position.x < minX) {
        currentNode.position.x = minX
      }
    }
  })
}

// 载入TP流程
const handleLoadFlow = (flowData: any[]) => {
  // 清空现有数据
  elements.value = []
  edges.value = []
  selectedNode.value = null
  selectedEdge.value = null

  // 设置当前流程信息
  if (flowData.length > 0) {
    const firstItem = flowData[0]
    currentFlowInfo.value = {
      emDescription: firstItem.em_description_cn || '',
      emVariable: firstItem.em_variable || '',
      opSequence: firstItem.op_sequence_under_em || '',
      tpSequence: firstItem.tp_sequence || ''
    }
  }

  // 转换TP数据为流程图节点
  const nodeMap = new Map<number, string>()

  flowData.forEach((item, index) => {
    const nodeId = (index + 1).toString()
    nodeMap.set(item.step_id, nodeId)

    const newNode: FlowNode = {
      id: nodeId,
      type: 'custom',
      label: item.step_description || `步骤${item.step_id}`,
      data: {
        toolbarPosition: Position.Top,
        name: item.step_description || `步骤${item.step_id}`,
        condition: item.motion_condition || '',
        action: item.motion_action || '',
        tpId: item.id  // 保存TP记录的ID
      },
      position: { x: 300, y: 50 + index * 150 },
      style: defaultNodeStyle,
    }

    elements.value.push(newNode)
  })

  // 处理跳转连接线
  flowData.forEach((item, index) => {
    if (item.skip_condition && item.skip_step) {
      const sourceNodeId = (index + 1).toString()
      const skipConditions = item.skip_condition.split(';').filter(Boolean)
      const skipSteps = item.skip_step.split(';').filter(Boolean)

      // 为每个跳转条件创建连接线
      skipConditions.forEach((condition, conditionIndex) => {
        if (conditionIndex < skipSteps.length) {
          const targetStepId = parseInt(skipSteps[conditionIndex])
          const targetNodeId = nodeMap.get(targetStepId)

          if (targetNodeId) {
            const edgeId = `e${sourceNodeId}-${targetNodeId}-${conditionIndex}`
            const newEdge: FlowEdge = {
              id: edgeId,
              source: sourceNodeId,
              target: targetNodeId,
              markerEnd: MarkerType.ArrowClosed,
              style: { strokeWidth: 2, stroke: '#409eff' },
              label: condition,
              data: { condition }
            }
            edges.value.push(newEdge)
          }
        }
      })
    }
  })

  // 添加默认的顺序连接线（如果没有跳转）
  for (let i = 0; i < elements.value.length - 1; i++) {
    const currentNode = elements.value[i]
    const nextNode = elements.value[i + 1]

    // 检查是否已经有从当前节点出发的连接线
    const hasOutgoingEdge = edges.value.some(edge => edge.source === currentNode.id)

    if (!hasOutgoingEdge) {
      const edgeId = `e${currentNode.id}-${nextNode.id}`
      const newEdge: FlowEdge = {
        id: edgeId,
        source: currentNode.id,
        target: nextNode.id,
        markerEnd: MarkerType.ArrowClosed,
        style: { strokeWidth: 2, stroke: '#409eff' },
        label: '',
        data: { condition: '' }
      }
      edges.value.push(newEdge)
    }
  }

  // 载入完成后自动应用智能布局
  setTimeout(() => {
    autoLayout()
  }, 100)
}

// 打开载入对话框
const openLoadDialog = () => {
  loadDialogVisible.value = true
}

// 打开保存对话框
const openSaveDialog = () => {
  saveDialogVisible.value = true
}

// 删除当前流程
const deleteCurrentFlow = async () => {
  if (!currentFlowInfo.value) {
    ElMessage.warning('没有可删除的流程')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除流程 "${currentFlowInfo.value.emDescription}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await tpControlApi.deleteFlow(
      currentFlowInfo.value.emVariable,
      currentFlowInfo.value.opSequence
    )

    if (response.success) {
      ElMessage.success('流程删除成功')
      // 清空当前流程
      elements.value = []
      edges.value = []
      selectedNode.value = null
      selectedEdge.value = null
      currentFlowInfo.value = null
    } else {
      ElMessage.error('删除失败: ' + response.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除流程失败:', error)
      ElMessage.error('删除流程失败')
    }
  }
}

// 创建测试布局
const createTestLayout = () => {
  // 清空现有数据
  elements.value = []
  edges.value = []
  selectedNode.value = null
  selectedEdge.value = null
  currentFlowInfo.value = null

  // 创建测试节点
  const testNodes: FlowNode[] = [
    {
      id: '1',
      type: 'custom',
      label: '开始',
      data: {
        toolbarPosition: Position.Top,
        name: '开始',
        condition: '',
        action: '初始化系统'
      },
      position: { x: 300, y: 80 },
      style: { ...defaultNodeStyle, border: '2px solid #67c23a' },
    },
    {
      id: '2',
      type: 'custom',
      label: '条件判断',
      data: {
        toolbarPosition: Position.Right,
        name: '条件判断',
        condition: 'SV1在位置1',
        action: '检查状态'
      },
      position: { x: 300, y: 260 },
      style: { ...defaultNodeStyle, border: '2px solid #e6a23c' },
    },
    {
      id: '3',
      type: 'custom',
      label: '成功分支',
      data: {
        toolbarPosition: Position.Bottom,
        name: '成功分支',
        condition: 'bDoneAction[1]',
        action: '执行成功操作'
      },
      position: { x: 100, y: 440 },
      style: { ...defaultNodeStyle, border: '2px solid #67c23a' },
    },
    {
      id: '4',
      type: 'custom',
      label: '失败分支',
      data: {
        toolbarPosition: Position.Bottom,
        name: '失败分支',
        condition: 'bAlm[10]',
        action: '处理错误'
      },
      position: { x: 300, y: 440 },
      style: { ...defaultNodeStyle, border: '2px solid #f56c6c' },
    },
    {
      id: '5',
      type: 'custom',
      label: '超时分支',
      data: {
        toolbarPosition: Position.Bottom,
        name: '超时分支',
        condition: 'bTimeout',
        action: '超时处理'
      },
      position: { x: 500, y: 440 },
      style: { ...defaultNodeStyle, border: '2px solid #e6a23c' },
    },
    {
      id: '6',
      type: 'custom',
      label: '汇聚节点',
      data: {
        toolbarPosition: Position.Bottom,
        name: '汇聚节点',
        condition: '',
        action: '最终处理'
      },
      position: { x: 300, y: 620 },
      style: { ...defaultNodeStyle, border: '2px solid #909399' },
    }
  ]

  // 创建测试连接线
  const testEdges: FlowEdge[] = [
    {
      id: 'e1-2',
      source: '1',
      target: '2',
      markerEnd: MarkerType.ArrowClosed,
      style: { strokeWidth: 2, stroke: '#409eff' },
      label: '',
      data: { condition: '' }
    },
    {
      id: 'e2-3',
      source: '2',
      target: '3',
      markerEnd: MarkerType.ArrowClosed,
      style: { strokeWidth: 2, stroke: '#67c23a' },
      label: '成功',
      data: { condition: '成功' }
    },
    {
      id: 'e2-4',
      source: '2',
      target: '4',
      markerEnd: MarkerType.ArrowClosed,
      style: { strokeWidth: 2, stroke: '#f56c6c' },
      label: '失败',
      data: { condition: '失败' }
    },
    {
      id: 'e2-5',
      source: '2',
      target: '5',
      markerEnd: MarkerType.ArrowClosed,
      style: { strokeWidth: 2, stroke: '#e6a23c' },
      label: '超时',
      data: { condition: '超时' }
    },
    {
      id: 'e3-6',
      source: '3',
      target: '6',
      markerEnd: MarkerType.ArrowClosed,
      style: { strokeWidth: 2, stroke: '#409eff' },
      label: '完成',
      data: { condition: '完成' }
    },
    {
      id: 'e4-6',
      source: '4',
      target: '6',
      markerEnd: MarkerType.ArrowClosed,
      style: { strokeWidth: 2, stroke: '#409eff' },
      label: '错误处理完成',
      data: { condition: '错误处理完成' }
    },
    {
      id: 'e5-6',
      source: '5',
      target: '6',
      markerEnd: MarkerType.ArrowClosed,
      style: { strokeWidth: 2, stroke: '#409eff' },
      label: '超时处理完成',
      data: { condition: '超时处理完成' }
    }
  ]

  elements.value = testNodes
  edges.value = testEdges

  // 应用自动布局
  setTimeout(() => {
    autoLayout()
  }, 100)
}


</script>

<template>
  <div class="flow-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="addNewNode">
          <el-icon><Plus /></el-icon>
          添加节点
        </el-button>
        <el-button
          type="danger"
          :disabled="!selectedNode && !selectedEdge"
          @click="selectedNode ? deleteNode(selectedNode.id) : selectedEdge && deleteEdge(selectedEdge.id)"
        >
          <el-icon><Delete /></el-icon>
          {{ selectedNode ? '删除节点' : selectedEdge ? '删除连接线' : '删除' }}
        </el-button>
        <el-button @click="clearSelection">
          <el-icon><Close /></el-icon>
          清空选择
        </el-button>
        <el-button type="info" @click="autoLayout">
          <el-icon><Sort /></el-icon>
          自动排列
        </el-button>
        <el-button type="success" @click="openLoadDialog">
          <el-icon><Upload /></el-icon>
          载入TP流程
        </el-button>
        <el-button type="warning" @click="openSaveDialog">
          <el-icon><Download /></el-icon>
          保存TP流程
        </el-button>
        <el-button type="info" @click="createTestLayout">
          <el-icon><DataBoard /></el-icon>
          测试布局
        </el-button>
      </div>

      <!-- 当前流程信息 -->
      <div v-if="currentFlowInfo" class="current-flow-info">
        <div class="flow-info-content">
          <span class="flow-title">{{ currentFlowInfo.emDescription }}</span>
          <span class="flow-meta">{{ currentFlowInfo.emVariable }} - {{ currentFlowInfo.opSequence }}</span>
        </div>
        <el-button
          type="danger"
          size="small"
          @click="deleteCurrentFlow"
          title="删除整个流程"
        >
          <el-icon><Delete /></el-icon>
          删除流程
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 流程图区域 -->
      <div class="flow-area">
        <VueFlow
          v-model:nodes="elements"
          v-model:edges="edges"
          fit-view-on-init
          class="vue-flow-container"
          :default-edge-options="{ markerEnd: MarkerType.ArrowClosed }"
          :connection-line-style="{ strokeWidth: 2, stroke: '#409eff' }"
          @pane-click="clearSelection"
        >
          <template #node-custom="nodeProps">
            <CustomNode
              :id="nodeProps.id"
              :data="nodeProps.data"
              :label="nodeProps.label"
              :selected="selectedNode?.id === nodeProps.id"
              @delete="deleteNode"
            />
          </template>

          <Background />
          <Controls />
          <MiniMap />
        </VueFlow>
      </div>

      <!-- 属性面板 -->
      <div class="properties-panel">
        <PropertiesPanel
          :selected-node="selectedNode"
          :selected-edge="selectedEdge"
          @update-node="updateNodeData"
          @update-edge="updateEdgeData"
        />
      </div>
    </div>

    <!-- 载入TP流程对话框 -->
    <LoadTPFlowDialog
      v-model:visible="loadDialogVisible"
      @load-flow="handleLoadFlow"
    />

    <!-- 保存TP流程对话框 -->
    <SaveTPFlowDialog
      v-model:visible="saveDialogVisible"
      :nodes="elements"
      :edges="edges"
      :current-flow-info="currentFlowInfo"
      @save-success="() => {}"
    />
  </div>
</template>

<style scoped>
.flow-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  padding: 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.current-flow-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
}

.flow-info-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.flow-title {
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
}

.flow-meta {
  font-size: 12px;
  color: #666;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.flow-area {
  flex: 1;
  position: relative;
}

.vue-flow-container {
  width: 100%;
  height: 100%;
}

.properties-panel {
  width: 300px;
  border-left: 1px solid #ddd;
  background: #fafafa;
  overflow-y: auto;
}

/* Vue Flow 样式优化 */
:deep(.vue-flow__edge-path) {
  stroke: #409eff;
  stroke-width: 2px;
}

:deep(.vue-flow__edge.selected .vue-flow__edge-path) {
  stroke: #67c23a;
  stroke-width: 3px;
}

:deep(.vue-flow__connection-line) {
  stroke: #409eff;
  stroke-width: 2px;
  stroke-dasharray: 5,5;
}

:deep(.vue-flow__handle) {
  width: 10px;
  height: 10px;
  background: #409eff;
  border: 2px solid white;
  border-radius: 50%;
}

:deep(.vue-flow__handle:hover) {
  background: #67c23a;
  transform: scale(1.2);
}

:deep(.vue-flow__handle.connectable) {
  cursor: crosshair;
}

:deep(.vue-flow__handle.connecting) {
  background: #67c23a;
}

/* 边标签样式 */
:deep(.vue-flow__edge-text) {
  font-size: 12px;
  fill: #303133;
  font-weight: 500;
}

:deep(.vue-flow__edge-textbg) {
  fill: white;
  stroke: #409eff;
  stroke-width: 1px;
  rx: 4px;
  ry: 4px;
}

:deep(.vue-flow__edge.selected .vue-flow__edge-textbg) {
  fill: #f0f9ff;
  stroke: #67c23a;
  stroke-width: 2px;
}

/* 修复Controls组件样式 */
:deep(.vue-flow__controls) {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

:deep(.vue-flow__controls-button) {
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

:deep(.vue-flow__controls-button:hover) {
  border-color: #409eff;
  color: #409eff;
}

:deep(.vue-flow__controls-button svg) {
  width: 16px;
  height: 16px;
}
</style>

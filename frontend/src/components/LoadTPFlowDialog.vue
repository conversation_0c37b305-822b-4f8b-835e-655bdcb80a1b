<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { tpControlApi } from '@/services/api'
import type { TPControlLogic } from '@/types'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'load-flow', flowData: TPControlLogic[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 分组数据
const groupedData = ref<Record<string, TPControlLogic[]>>({})
const loading = ref(false)
const selectedFlow = ref<{
  emVariable: string
  opSequence: string
  emDescription: string
  flowData: TPControlLogic[]
} | null>(null)

// 获取分组数据
const fetchGroupedData = async () => {
  loading.value = true
  try {
    const response = await tpControlApi.getGroupedByEm()
    if (response.success) {
      groupedData.value = response.data || {}
    } else {
      ElMessage.error('获取TP流程数据失败')
    }
  } catch (error) {
    console.error('获取TP流程数据失败:', error)
    ElMessage.error('获取TP流程数据失败')
  } finally {
    loading.value = false
  }
}

// 处理流程选择
const handleFlowSelect = (emDescription: string, flowData: TPControlLogic[]) => {
  if (flowData.length === 0) return
  
  const firstItem = flowData[0]
  selectedFlow.value = {
    emVariable: firstItem.em_variable || '',
    opSequence: firstItem.op_sequence_under_em || '',
    emDescription,
    flowData
  }
}

// 确认载入
const handleConfirm = () => {
  if (!selectedFlow.value) {
    ElMessage.warning('请选择要载入的TP流程')
    return
  }
  
  emit('load-flow', selectedFlow.value.flowData)
  handleClose()
  ElMessage.success('TP流程载入成功')
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  selectedFlow.value = null
}

// 组件挂载时获取数据
onMounted(() => {
  if (props.visible) {
    fetchGroupedData()
  }
})

// 监听对话框显示状态
const handleDialogOpen = () => {
  fetchGroupedData()
}

// 格式化步骤信息
const formatStepInfo = (item: TPControlLogic) => {
  const parts = []
  if (item.step_id !== undefined) parts.push(`步骤${item.step_id}`)
  if (item.step_description) parts.push(item.step_description)
  return parts.join(': ')
}

// 格式化条件信息
const formatConditionInfo = (item: TPControlLogic) => {
  const parts = []
  if (item.motion_condition) parts.push(`条件: ${item.motion_condition}`)
  if (item.motion_action) parts.push(`动作: ${item.motion_action}`)
  return parts.join(' | ')
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    title="载入TP流程"
    width="800px"
    @update:model-value="handleClose"
    @open="handleDialogOpen"
  >
    <div class="load-dialog-content">
      <div v-loading="loading" class="flow-list">
        <div v-if="Object.keys(groupedData).length === 0" class="empty-state">
          <el-empty description="暂无TP流程数据" />
        </div>
        
        <div v-else class="flow-groups">
          <div
            v-for="(flows, emDescription) in groupedData"
            :key="emDescription"
            class="flow-group"
          >
            <div class="group-header">
              <h4>{{ emDescription }}</h4>
              <el-tag size="small">{{ flows.length }} 个步骤</el-tag>
            </div>
            
            <div class="flow-items">
              <div
                v-for="item in flows.slice(0, 3)"
                :key="item.id"
                class="flow-item-preview"
              >
                <div class="step-info">{{ formatStepInfo(item) }}</div>
                <div class="condition-info">{{ formatConditionInfo(item) }}</div>
              </div>
              
              <div v-if="flows.length > 3" class="more-items">
                还有 {{ flows.length - 3 }} 个步骤...
              </div>
            </div>
            
            <div class="group-actions">
              <el-button
                type="primary"
                size="small"
                :class="{ 'is-selected': selectedFlow?.emDescription === emDescription }"
                @click="handleFlowSelect(emDescription, flows)"
              >
                {{ selectedFlow?.emDescription === emDescription ? '已选择' : '选择此流程' }}
              </el-button>
              
              <div class="flow-meta">
                <span v-if="flows[0]?.em_variable">EM: {{ flows[0].em_variable }}</span>
                <span v-if="flows[0]?.op_sequence_under_em">OP: {{ flows[0].op_sequence_under_em }}</span>
                <span v-if="flows[0]?.tp_sequence">TP: {{ flows[0].tp_sequence }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 选中的流程信息 -->
      <div v-if="selectedFlow" class="selected-flow-info">
        <el-divider content-position="left">选中的流程</el-divider>
        <div class="selected-info">
          <p><strong>EM描述:</strong> {{ selectedFlow.emDescription }}</p>
          <p><strong>EM变量:</strong> {{ selectedFlow.emVariable }}</p>
          <p><strong>OP序列:</strong> {{ selectedFlow.opSequence }}</p>
          <p><strong>步骤数量:</strong> {{ selectedFlow.flowData.length }}</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="!selectedFlow"
          @click="handleConfirm"
        >
          载入流程
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.load-dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

.flow-groups {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.flow-group {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.group-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.flow-items {
  margin-bottom: 12px;
}

.flow-item-preview {
  padding: 8px;
  background: white;
  border-radius: 4px;
  margin-bottom: 8px;
  border-left: 3px solid #409eff;
}

.step-info {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.condition-info {
  font-size: 12px;
  color: #606266;
}

.more-items {
  text-align: center;
  color: #909399;
  font-size: 12px;
  padding: 8px;
}

.group-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flow-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.flow-meta span {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.selected-flow-info {
  margin-top: 16px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #b3d8ff;
}

.selected-info p {
  margin: 4px 0;
  font-size: 14px;
}

.is-selected {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>

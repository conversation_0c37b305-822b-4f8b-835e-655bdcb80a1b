<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { tpControlApi } from '@/services/api'
import type { TPControlLogic, TPControlLogicCreate } from '@/types'

interface FlowNode {
  id: string
  type: string
  label: string
  data: {
    name: string
    condition: string
    action: string
    tpId?: number  // TP记录的ID
  }
  position: { x: number; y: number }
}

interface FlowEdge {
  id: string
  source: string
  target: string
  data?: {
    condition: string
  }
}

interface Props {
  visible: boolean
  nodes: FlowNode[]
  edges: FlowEdge[]
  currentFlowInfo?: {
    emDescription: string
    emVariable: string
    opSequence: string
    tpSequence: string
  } | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'save-success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const formData = ref({
  emDescription: '',
  emVariable: '',
  opSequence: '',
  tpSequence: ''
})

// 监听当前流程信息变化，自动填充表单
watch(() => props.currentFlowInfo, (newInfo) => {
  if (newInfo) {
    formData.value = {
      emDescription: newInfo.emDescription,
      emVariable: newInfo.emVariable,
      opSequence: newInfo.opSequence,
      tpSequence: newInfo.tpSequence
    }
  }
}, { immediate: true })

const loading = ref(false)

// 验证表单
const isFormValid = computed(() => {
  return formData.value.emDescription && 
         formData.value.emVariable && 
         formData.value.opSequence && 
         formData.value.tpSequence
})

// 预览数据
const previewData = computed(() => {
  if (props.nodes.length === 0) return []
  
  // 构建节点映射
  const nodeMap = new Map<string, FlowNode>()
  props.nodes.forEach(node => {
    nodeMap.set(node.id, node)
  })
  
  // 构建跳转映射
  const jumpMap = new Map<string, { condition: string; targetStepId: number }[]>()
  props.edges.forEach(edge => {
    const sourceNode = nodeMap.get(edge.source)
    const targetNode = nodeMap.get(edge.target)
    
    if (sourceNode && targetNode) {
      const sourceStepId = parseInt(sourceNode.id) * 10 // 简单的步骤ID生成规则
      const targetStepId = parseInt(targetNode.id) * 10
      
      if (!jumpMap.has(edge.source)) {
        jumpMap.set(edge.source, [])
      }
      
      jumpMap.get(edge.source)!.push({
        condition: edge.data?.condition || '',
        targetStepId
      })
    }
  })
  
  // 生成TP数据
  return props.nodes.map((node, index) => {
    const stepId = index * 10 // 步骤ID: 0, 10, 20, 30...
    const jumps = jumpMap.get(node.id) || []
    
    // 构建跳转条件和目标
    const skipConditions = jumps.map(jump => jump.condition).filter(Boolean)
    const skipSteps = jumps.map(jump => jump.targetStepId.toString())
    
    const tpData: any = {
      step_id: stepId,
      step_description: node.data.name || `步骤${stepId}`,
      motion_condition: node.data.condition || '',
      motion_action: node.data.action || '',
      skip_condition: skipConditions.join(';') || '',
      skip_step: skipSteps.join(';') || '',
      tp_sequence: formData.value.tpSequence,
      em_description_cn: formData.value.emDescription,
      em_variable: formData.value.emVariable,
      op_sequence_under_em: formData.value.opSequence
    }

    // 如果节点有tpId，说明是更新操作
    if (node.data.tpId) {
      tpData.id = node.data.tpId
    }

    return tpData
  })
})

// 保存流程
const handleSave = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请填写完整的流程信息')
    return
  }
  
  if (props.nodes.length === 0) {
    ElMessage.warning('没有可保存的流程节点')
    return
  }
  
  loading.value = true
  try {
    // 分别处理更新和创建操作
    const updateItems: any[] = []
    const createItems: any[] = []

    previewData.value.forEach(item => {
      if (item.id) {
        updateItems.push(item)
      } else {
        createItems.push(item)
      }
    })

    // 处理更新操作
    for (const item of updateItems) {
      await tpControlApi.update(item.id, item)
    }

    // 处理创建操作
    if (createItems.length > 0) {
      await tpControlApi.createFlowBatch(createItems as TPControlLogicCreate[])
    }

    ElMessage.success(`TP流程保存成功 (更新${updateItems.length}条，新增${createItems.length}条)`)
    emit('save-success')
    handleClose()
  } catch (error) {
    console.error('保存TP流程失败:', error)
    ElMessage.error('保存TP流程失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  // 重置表单
  formData.value = {
    emDescription: '',
    emVariable: '',
    opSequence: '',
    tpSequence: ''
  }
}

// 常用EM变量模板
const emVariableTemplates = [
  'io_stEmBtryIn',
  'io_stEmBtryOut',
  'io_stEmBtryMove',
  'io_stEmConvey',
  'io_stEmLift'
]

// 常用OP序列模板
const opSequenceTemplates = [
  'OP[1]',
  'OP[2]',
  'OP[3]',
  'OP[4]',
  'OP[5]'
]

// 插入模板
const insertTemplate = (field: keyof typeof formData.value, template: string) => {
  formData.value[field] = template
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    title="保存TP流程"
    width="900px"
    @update:model-value="handleClose"
  >
    <div class="save-dialog-content">
      <!-- 流程信息表单 -->
      <div class="form-section">
        <h4>流程信息</h4>
        <el-form :model="formData" label-width="120px" size="small">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="EM描述" required>
                <el-input
                  v-model="formData.emDescription"
                  placeholder="请输入EM中文描述"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="TP序列" required>
                <el-input
                  v-model="formData.tpSequence"
                  placeholder="如: io_stUm.TP[1]"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="EM变量" required>
                <el-input
                  v-model="formData.emVariable"
                  placeholder="请输入EM变量"
                  clearable
                />
                <div class="template-buttons">
                  <span class="template-label">常用:</span>
                  <el-button
                    v-for="template in emVariableTemplates"
                    :key="template"
                    size="small"
                    text
                    @click="insertTemplate('emVariable', template)"
                  >
                    {{ template }}
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="OP序列" required>
                <el-input
                  v-model="formData.opSequence"
                  placeholder="请输入OP序列"
                  clearable
                />
                <div class="template-buttons">
                  <span class="template-label">常用:</span>
                  <el-button
                    v-for="template in opSequenceTemplates"
                    :key="template"
                    size="small"
                    text
                    @click="insertTemplate('opSequence', template)"
                  >
                    {{ template }}
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      
      <!-- 流程预览 -->
      <div class="preview-section">
        <h4>流程预览 ({{ previewData.length }} 个步骤)</h4>
        <div class="preview-table">
          <el-table :data="previewData" size="small" max-height="300">
            <el-table-column prop="step_id" label="步骤ID" width="80" />
            <el-table-column prop="step_description" label="步骤描述" width="120" />
            <el-table-column prop="motion_condition" label="运动条件" width="150" show-overflow-tooltip />
            <el-table-column prop="motion_action" label="运动动作" width="120" show-overflow-tooltip />
            <el-table-column prop="skip_condition" label="跳转条件" width="120" show-overflow-tooltip />
            <el-table-column prop="skip_step" label="跳转目标" width="100" />
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!isFormValid"
          @click="handleSave"
        >
          保存流程
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.save-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-section h4,
.preview-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.template-buttons {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.template-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.preview-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>

<template>
  <el-container class="app-layout">
    <!-- 顶部导航 -->
    <el-header class="app-header">
      <div class="header-left">
        <el-button
          :icon="collapsed ? Expand : Fold"
          circle
          @click="toggleCollapse"
          style="color: white; border: none; background: transparent;"
        />
        <h1 class="app-logo">PLC自动编码系统</h1>
      </div>

      <!-- <div class="header-right">
        <el-tooltip content="帮助文档">
          <el-button :icon="QuestionFilled" circle text style="color: white;" />
        </el-tooltip>
        <el-tooltip content="GitHub">
          <el-button :icon="Link" circle text style="color: white;" />
        </el-tooltip>
      </div> -->
    </el-header>

    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="collapsed ? '64px' : '200px'" class="app-aside">
        <el-menu
          :default-active="$route.path"
          :collapse="collapsed"
          :router="true"
          background-color="#001529"
          text-color="#fff"
          active-text-color="#409eff"
        >
          <el-menu-item index="/">
            <el-icon><Odometer /></el-icon>
            <template #title>仪表板</template>
          </el-menu-item>

          <el-menu-item index="/tp-control">
            <el-icon><Setting /></el-icon>
            <template #title>TP控制逻辑</template>
          </el-menu-item>

          <el-menu-item index="/op-control">
            <el-icon><Tools /></el-icon>
            <template #title>OP控制逻辑</template>
          </el-menu-item>

          <el-menu-item index="/actuator-map">
            <el-icon><Connection /></el-icon>
            <template #title>执行器变量映射</template>
          </el-menu-item>

          <el-menu-item index="/code-generation">
            <el-icon><DocumentCopy /></el-icon>
            <template #title>代码生成</template>
          </el-menu-item>

          <el-menu-item index="/flow-designer">
            <el-icon><Share /></el-icon>
            <template #title>流程设计器</template>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="app-main">
        <div class="app-content" :class="{ 'fullscreen': isFlowDesigner }">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import {
  Expand,
  Fold,
  QuestionFilled,
  Link,
  Odometer,
  Setting,
  Tools,
  Connection,
  DocumentCopy,
  Share
} from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useAppStore } from '@/stores/app'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const appStore = useAppStore()
const { collapsed } = storeToRefs(appStore)
const route = useRoute()

const toggleCollapse = () => {
  appStore.setCollapsed(!collapsed.value)
}

// 判断是否是流程设计器页面
const isFlowDesigner = computed(() => route.path === '/flow-designer')
</script>

<style scoped>
.app-layout {
  height: 100vh;
}

.app-header {
  background: #001529;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-logo {
  color: white;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.app-aside {
  background: #001529;
  transition: width 0.3s;
}

.app-main {
  background: #f0f2f5;
  padding: 0;
}

.app-content {
  margin: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 108px);
}

/* 流程设计器全屏样式 */
.app-content.fullscreen {
  margin: 0;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  min-height: calc(100vh - 60px);
  height: calc(100vh - 60px);
}

.el-menu {
  border-right: none;
}
</style>

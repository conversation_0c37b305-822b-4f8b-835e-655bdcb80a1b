// 通用类型定义

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error_code?: string
  details?: any
}

export interface PaginationParams {
  page: number
  size: number
}

export interface ListResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// TP控制逻辑类型
export interface TPControlLogic {
  id: number
  step_id: number
  step_description?: string
  motion_condition?: string
  motion_action?: string
  skip_condition?: string
  skip_step?: string
  tp_sequence?: string
  em_description_cn?: string
  em_variable?: string
  op_sequence_under_em?: string
  created_at: string
  updated_at: string
}

export interface TPControlLogicCreate {
  step_id: number
  step_description?: string
  motion_condition?: string
  motion_action?: string
  skip_condition?: string
  skip_step?: string
  tp_sequence?: string
  em_description_cn?: string
  em_variable?: string
  op_sequence_under_em?: string
}

export interface TPControlLogicUpdate {
  step_id?: number
  step_description?: string
  motion_condition?: string
  motion_action?: string
  skip_condition?: string
  skip_step?: string
  tp_sequence?: string
  em_description_cn?: string
  em_variable?: string
  op_sequence_under_em?: string
}

export interface TPControlLogicCreate {
  step_id: number
  step_description?: string
  motion_condition?: string
  motion_action?: string
  skip_condition?: string
  skip_step?: string
  tp_sequence?: string
  em_description_cn?: string
  em_variable?: string
  op_sequence_under_em?: string
}

export interface TPControlLogicUpdate {
  step_id?: number
  step_description?: string
  motion_condition?: string
  motion_action?: string
  skip_condition?: string
  skip_step?: string
  tp_sequence?: string
  em_description_cn?: string
  em_variable?: string
  op_sequence_under_em?: string
}

// OP控制逻辑类型
export interface OPControlLogic {
  id: number;
  step_id: number;
  step_description?: string;
  watchdog_mask?: string;
  action_description?: string;
  normal_interlock_condition?: string;
  recovery_interlock_condition?: string;
  alarm_condition?: string;
  skip_condition?: string;
  em_description_cn?: string;
  op_sequence_under_em?: string;
  em_variable?: string;
  recovery_interlock_action?: string;
  created_at: string;
  updated_at: string;
}

export interface OPControlLogicCreate {
  step_id: number;
  step_description?: string;
  watchdog_mask?: string;
  action_description?: string;
  normal_interlock_condition?: string;
  recovery_interlock_condition?: string;
  alarm_condition?: string;
  skip_condition?: string;
  em_description_cn?: string;
  op_sequence_under_em?: string;
  em_variable?: string;
  recovery_interlock_action?: string;
}

export interface OPControlLogicUpdate {
  step_id?: number;
  step_description?: string;
  watchdog_mask?: string;
  action_description?: string;
  normal_interlock_condition?: string;
  recovery_interlock_condition?: string;
  alarm_condition?: string;
  skip_condition?: string;
  em_description_cn?: string;
  op_sequence_under_em?: string;
  em_variable?: string;
  recovery_interlock_action?: string;
}

// 执行器变量映射类型
export interface ActuatorVariableMap {
  id: number;
  parent_struct?: string;
  actuator?: string;
  command_variable?: string;
  command_description_cn?: string;
  created_at: string;
  updated_at: string;
}

export interface ActuatorVariableMapCreate {
  parent_struct?: string;
  actuator?: string;
  command_variable?: string;
  command_description_cn?: string;
}

export interface ActuatorVariableMapUpdate {
  parent_struct?: string;
  actuator?: string;
  command_variable?: string;
  command_description_cn?: string;
}

// 代码生成类型
export interface CodeGenerationRequest {
  table_type: 'tp' | 'op';
  em_description?: string;
  include_test?: boolean;
}

export interface CodeGenerationResponse {
  code: string;
  filename: string;
  warnings: string[];
  unmapped_conditions: string[];
}

// 表格筛选类型
export interface TPControlFilters {
  step_id?: number;
  em_description?: string;
  em_variable?: string;
}

export interface OPControlFilters {
  step_id?: number;
  em_description?: string;
  em_variable?: string;
}

export interface ActuatorMapFilters {
  parent_struct?: string;
  actuator?: string;
  keyword?: string;
}

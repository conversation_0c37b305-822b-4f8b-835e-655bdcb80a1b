import axios, { AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import {
  ApiResponse,
  ListResponse,
  TPControlLogic,
  TPControlLogicCreate,
  TPControlLogicUpdate,
  TPControlFilters,
  OPControlLogic,
  OPControlLogicCreate,
  OPControlLogicUpdate,
  OPControlFilters,
  ActuatorVariableMap,
  ActuatorVariableMapCreate,
  ActuatorVariableMapUpdate,
  ActuatorMapFilters,
  CodeGenerationRequest,
  CodeGenerationResponse,
  PaginationParams
} from '../types'

// 创建axios实例
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    const errorMessage = error.response?.data?.message || error.message || '请求失败'
    ElMessage.error(errorMessage)
    return Promise.reject(error)
  }
)

// TP控制逻辑API
export const tpControlApi = {
  // 获取列表
  getList: (params: PaginationParams & TPControlFilters): Promise<ListResponse<TPControlLogic>> => {
    return api.get('/tp-control/', { params }).then(res => res.data);
  },

  // 获取单个记录
  getById: (id: number): Promise<TPControlLogic> => {
    return api.get(`/tp-control/${id}`).then(res => res.data);
  },

  // 创建记录
  create: (data: TPControlLogicCreate): Promise<TPControlLogic> => {
    return api.post('/tp-control/', data).then(res => res.data);
  },

  // 更新记录
  update: (id: number, data: TPControlLogicUpdate): Promise<TPControlLogic> => {
    return api.put(`/tp-control/${id}`, data).then(res => res.data);
  },

  // 删除记录
  delete: (id: number): Promise<ApiResponse> => {
    return api.delete(`/tp-control/${id}`).then(res => res.data);
  },

  // 按EM分组获取
  getGroupedByEm: (): Promise<ApiResponse> => {
    return api.get('/tp-control/grouped/by-em').then(res => res.data);
  },

  // 获取指定EM和OP的TP流程
  getFlow: (emVariable: string, opSequence: string): Promise<ApiResponse<TPControlLogic[]>> => {
    return api.get(`/tp-control/flow/${emVariable}/${opSequence}`).then(res => res.data);
  },

  // 删除指定EM和OP的TP流程
  deleteFlow: (emVariable: string, opSequence: string): Promise<ApiResponse> => {
    return api.delete(`/tp-control/flow/${emVariable}/${opSequence}`).then(res => res.data);
  },

  // 批量创建TP流程
  createFlowBatch: (tpList: TPControlLogicCreate[]): Promise<ApiResponse<TPControlLogic[]>> => {
    return api.post('/tp-control/flow/batch', tpList).then(res => res.data);
  },
};

// OP控制逻辑API
export const opControlApi = {
  // 获取列表
  getList: (params: PaginationParams & OPControlFilters): Promise<ListResponse<OPControlLogic>> => {
    return api.get('/op-control/', { params }).then(res => res.data);
  },

  // 获取单个记录
  getById: (id: number): Promise<OPControlLogic> => {
    return api.get(`/op-control/${id}`).then(res => res.data);
  },

  // 创建记录
  create: (data: OPControlLogicCreate): Promise<OPControlLogic> => {
    return api.post('/op-control/', data).then(res => res.data);
  },

  // 更新记录
  update: (id: number, data: OPControlLogicUpdate): Promise<OPControlLogic> => {
    return api.put(`/op-control/${id}`, data).then(res => res.data);
  },

  // 删除记录
  delete: (id: number): Promise<ApiResponse> => {
    return api.delete(`/op-control/${id}`).then(res => res.data);
  },

  // 按EM分组获取
  getGroupedByEm: (): Promise<ApiResponse> => {
    return api.get('/op-control/grouped/by-em').then(res => res.data);
  },
};

// 执行器变量映射API
export const actuatorMapApi = {
  // 获取列表
  getList: (params: PaginationParams & ActuatorMapFilters): Promise<ListResponse<ActuatorVariableMap>> => {
    return api.get('/actuator-map/', { params }).then(res => res.data);
  },

  // 获取单个记录
  getById: (id: number): Promise<ActuatorVariableMap> => {
    return api.get(`/actuator-map/${id}`).then(res => res.data);
  },

  // 创建记录
  create: (data: ActuatorVariableMapCreate): Promise<ActuatorVariableMap> => {
    return api.post('/actuator-map/', data).then(res => res.data);
  },

  // 更新记录
  update: (id: number, data: ActuatorVariableMapUpdate): Promise<ActuatorVariableMap> => {
    return api.put(`/actuator-map/${id}`, data).then(res => res.data);
  },

  // 删除记录
  delete: (id: number): Promise<ApiResponse> => {
    return api.delete(`/actuator-map/${id}`).then(res => res.data);
  },

  // 获取映射字典
  getMappingDict: (): Promise<ApiResponse> => {
    return api.get('/actuator-map/mapping/dict').then(res => res.data);
  },
};

// 代码生成API
export const codeGenerationApi = {
  // 生成TP代码
  generateTP: (data: CodeGenerationRequest): Promise<CodeGenerationResponse> => {
    return api.post('/code-generation/tp', data).then(res => res.data);
  },

  // 生成OP代码
  generateOP: (data: CodeGenerationRequest): Promise<CodeGenerationResponse> => {
    return api.post('/code-generation/op', data).then(res => res.data);
  },

  // 下载TP代码
  downloadTP: (em_description?: string): string => {
    const params = em_description ? `?em_description=${encodeURIComponent(em_description)}` : '';
    return `/api/v1/code-generation/tp/download${params}`;
  },

  // 下载OP代码
  downloadOP: (em_description?: string): string => {
    const params = em_description ? `?em_description=${encodeURIComponent(em_description)}` : '';
    return `/api/v1/code-generation/op/download${params}`;
  },

  // 验证条件映射
  validateConditions: (): Promise<ApiResponse> => {
    return api.get('/code-generation/validate').then(res => res.data);
  },
};

// 通用API
export const commonApi = {
  // 健康检查
  healthCheck: (): Promise<any> => {
    return api.get('/health', { baseURL: '/' }).then(res => res.data);
  },
};

export default api;

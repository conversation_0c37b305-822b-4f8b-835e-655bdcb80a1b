<template>
  <div>
    <div class="page-header">
      <h1 class="page-title">执行器变量映射管理</h1>
      <p class="page-description">管理执行器变量映射关系，用于代码生成时的条件映射</p>
    </div>

    <!-- 搜索筛选 -->
    <el-card style="margin-bottom: 16px;">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
            v-model="filters.parent_struct"
            placeholder="父结构体"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filters.actuator"
            placeholder="执行器"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filters.keyword"
            placeholder="描述关键字"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-space>
            <el-button type="primary" :icon="Search" @click="handleSearch">
              搜索
            </el-button>
            <el-button :icon="Refresh" @click="handleReset">
              重置
            </el-button>
          </el-space>
        </el-col>
      </el-row>
    </el-card>

    <!-- 说明信息 -->
    <el-card style="margin-bottom: 16px;">
      <h4>字段说明：</h4>
      <ul>
        <li><el-tag type="success">Y</el-tag> - 动态拼接（父结构体：拼接EM变量；执行器：拼接执行器名称）</li>
        <li><el-tag type="danger">N</el-tag> - 不拼接</li>
        <li><el-tag type="warning">OP</el-tag> - 拼接OP序列变量</li>
        <li><el-tag type="info">其他值</el-tag> - 直接使用该值</li>
      </ul>
    </el-card>

    <!-- 工具栏 -->
    <div class="table-toolbar">
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        新增映射记录
      </el-button>
      <el-button :icon="Refresh" @click="loadData">
        刷新
      </el-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <DataTable
        :data="data"
        :loading="loading"
        :table-layout="'auto'"
        :height="tableHeight"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />

        <el-table-column prop="parent_struct" label="父结构体" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.parent_struct" :type="getTagType(row.parent_struct)" size="small">
              {{ row.parent_struct }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="actuator" label="执行器" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.actuator" :type="getTagType(row.actuator)" size="small">
              {{ row.actuator }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="command_variable" label="命令变量" min-width="200" show-overflow-tooltip />

        <el-table-column prop="command_description_cn" label="命令描述(中文)" min-width="180" show-overflow-tooltip />

        <el-table-column prop="created_at" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column prop="updated_at" label="更新时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.updated_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="140" fixed="right" align="center">
          <template #default="{ row }">
            <el-space>
              <el-button type="primary" size="small" :icon="Edit" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-popconfirm
                title="确定要删除这条记录吗？"
                @confirm="handleDelete(row.id)"
              >
                <template #reference>
                  <el-button type="danger" size="small" :icon="Delete">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </el-space>
          </template>
        </el-table-column>
      </DataTable>
    </div>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 16px; justify-content: center;"
      @size-change="loadData"
      @current-change="loadData"
    />

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingRecord ? '编辑映射记录' : '新增映射记录'"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="父结构体" prop="parent_struct">
          <el-input
            v-model="form.parent_struct"
            placeholder="例如: Y, N, 或具体值"
          />
          <div class="form-help">Y=动态拼接EM变量, N=不拼接, 其他=直接使用该值</div>
        </el-form-item>

        <el-form-item label="执行器" prop="actuator">
          <el-input
            v-model="form.actuator"
            placeholder="例如: Y, N, OP"
          />
          <div class="form-help">Y=动态拼接执行器名称, N=不拼接, OP=拼接OP序列</div>
        </el-form-item>

        <el-form-item label="命令变量" prop="command_variable">
          <el-input
            v-model="form.command_variable"
            placeholder="例如: Ctrl.arbAToPos[1]"
          />
        </el-form-item>

        <el-form-item label="命令描述(中文)" prop="command_description_cn">
          <el-input
            v-model="form.command_description_cn"
            placeholder="例如: SV1在位置1"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import { actuatorMapApi } from '@/services/api'
import type { ActuatorVariableMap, ActuatorVariableMapCreate, ActuatorVariableMapUpdate } from '@/types'
import dayjs from 'dayjs'
import DataTable from '@/components/DataTable.vue'

const data = ref<ActuatorVariableMap[]>([])
const loading = ref(false)
const dialogVisible = ref(false)
const editingRecord = ref<ActuatorVariableMap | null>(null)
const formRef = ref()

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})

const filters = reactive({
  parent_struct: '',
  actuator: '',
  keyword: '',
})

const form = reactive<ActuatorVariableMapCreate>({
  parent_struct: '',
  actuator: '',
  command_variable: '',
  command_description_cn: '',
})

const rules = {
  command_variable: [
    { required: true, message: '请输入命令变量', trigger: 'blur' }
  ],
  command_description_cn: [
    { required: true, message: '请输入命令描述', trigger: 'blur' }
  ],
}

const getTagType = (value: string) => {
  switch (value) {
    case 'Y': return 'success'
    case 'N': return 'danger'
    case 'OP': return 'warning'
    default: return 'info'
  }
}

const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 计算表格高度，让表格占据剩余空间
const tableHeight = computed(() => {
  // 页面头部 + 搜索卡片 + 工具栏 + 分页 + 边距
  const fixedHeight = 120 + 80 + 60 + 60 + 32
  return `calc(100vh - ${fixedHeight}px)`
})

const loadData = async () => {
  try {
    loading.value = true
    const result = await actuatorMapApi.getList({
      page: pagination.current,
      size: pagination.pageSize,
      ...filters,
    })
    data.value = result.items
    pagination.total = result.total
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(filters, {
    parent_struct: '',
    actuator: '',
    keyword: '',
  })
  pagination.current = 1
  loadData()
}

const handleAdd = () => {
  editingRecord.value = null
  Object.assign(form, {
    parent_struct: '',
    actuator: '',
    command_variable: '',
    command_description_cn: '',
  })
  dialogVisible.value = true
}

const handleEdit = (record: ActuatorVariableMap) => {
  editingRecord.value = record
  Object.assign(form, record)
  dialogVisible.value = true
}

const handleDelete = async (id: number) => {
  try {
    await actuatorMapApi.delete(id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (editingRecord.value) {
      await actuatorMapApi.update(editingRecord.value.id, form as ActuatorVariableMapUpdate)
      ElMessage.success('更新成功')
    } else {
      await actuatorMapApi.create(form as ActuatorVariableMapCreate)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('操作失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 8px 0 0 0;
  color: var(--el-text-color-regular);
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-help {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
}

.table-container {
  height: calc(100vh - 320px);
  min-height: 400px;
  display: flex;
  flex-direction: column;
}
</style>

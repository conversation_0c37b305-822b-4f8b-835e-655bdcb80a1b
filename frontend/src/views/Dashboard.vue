<template>
  <div>
    <div class="page-header">
      <h1 class="page-title">仪表板</h1>
      <p class="page-description">系统概览和快速操作</p>
    </div>

    <el-row :gutter="16" v-loading="loading">
      <!-- 统计卡片 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="stats-card">
          <el-statistic
            title="TP控制逻辑"
            :value="stats.tpCount"
            value-style="color: #409eff"
          >
            <template #prefix>
              <el-icon><Setting /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="stats-card">
          <el-statistic
            title="OP控制逻辑"
            :value="stats.opCount"
            value-style="color: #67c23a"
          >
            <template #prefix>
              <el-icon><Tools /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="stats-card">
          <el-statistic
            title="执行器变量映射"
            :value="stats.actuatorCount"
            value-style="color: #e6a23c"
          >
            <template #prefix>
              <el-icon><Connection /></el-icon>
            </template>
          </el-statistic>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :lg="6">
        <el-card class="stats-card">
          <div class="stats-number">
            <el-icon v-if="hasUnmappedConditions" color="#f56c6c" size="24">
              <WarningFilled />
            </el-icon>
            <el-icon v-else color="#67c23a" size="24">
              <SuccessFilled />
            </el-icon>
          </div>
          <div class="stats-label">条件映射状态</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 条件映射验证 -->
    <el-row :gutter="16" style="margin-top: 24px">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>条件映射验证</span>
              <el-button
                type="primary"
                :icon="DocumentCopy"
                :loading="validating"
                @click="validateConditions"
              >
                验证映射
              </el-button>
            </div>
          </template>

          <div v-if="validationResult">
            <el-space direction="vertical" style="width: 100%">
              <el-alert
                v-if="validationResult.tp_unmapped?.length > 0"
                :title="`TP未映射条件: ${validationResult.tp_unmapped.length} 个`"
                type="warning"
                show-icon
              >
                <ul style="margin: 0; padding-left: 20px;">
                  <li v-for="(condition, index) in validationResult.tp_unmapped.slice(0, 5)" :key="index">
                    <el-text type="info" tag="code">{{ condition }}</el-text>
                  </li>
                  <li v-if="validationResult.tp_unmapped.length > 5">
                    ... 还有 {{ validationResult.tp_unmapped.length - 5 }} 个
                  </li>
                </ul>
              </el-alert>

              <el-alert
                v-if="validationResult.op_unmapped?.length > 0"
                :title="`OP未映射条件: ${validationResult.op_unmapped.length} 个`"
                type="warning"
                show-icon
              >
                <ul style="margin: 0; padding-left: 20px;">
                  <li v-for="(condition, index) in validationResult.op_unmapped.slice(0, 5)" :key="index">
                    <el-text type="info" tag="code">{{ condition }}</el-text>
                  </li>
                  <li v-if="validationResult.op_unmapped.length > 5">
                    ... 还有 {{ validationResult.op_unmapped.length - 5 }} 个
                  </li>
                </ul>
              </el-alert>

              <el-alert
                v-if="!hasUnmappedConditions"
                title="所有条件映射完整"
                description="当前系统中的所有条件都已正确映射，可以正常生成代码。"
                type="success"
                show-icon
              />
            </el-space>
          </div>

          <el-alert
            v-else
            title="点击验证映射按钮检查条件映射完整性"
            type="info"
            show-icon
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="16" style="margin-top: 24px">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>

          <el-space wrap>
            <el-button type="primary" :icon="Setting" @click="$router.push('/tp-control')">
              管理TP控制逻辑
            </el-button>
            <el-button type="primary" :icon="Tools" @click="$router.push('/op-control')">
              管理OP控制逻辑
            </el-button>
            <el-button type="primary" :icon="Connection" @click="$router.push('/actuator-map')">
              管理执行器映射
            </el-button>
            <el-button type="primary" :icon="DocumentCopy" @click="$router.push('/code-generation')">
              生成代码
            </el-button>
          </el-space>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Setting,
  Tools,
  Connection,
  DocumentCopy,
  WarningFilled,
  SuccessFilled
} from '@element-plus/icons-vue'
import { tpControlApi, opControlApi, actuatorMapApi, codeGenerationApi } from '@/services/api'

interface DashboardStats {
  tpCount: number
  opCount: number
  actuatorCount: number
  validationResult?: any
}

const stats = ref<DashboardStats>({
  tpCount: 0,
  opCount: 0,
  actuatorCount: 0,
})

const loading = ref(true)
const validating = ref(false)
const validationResult = ref<any>(null)

const hasUnmappedConditions = computed(() => {
  return validationResult.value &&
    (validationResult.value.tp_unmapped?.length > 0 || validationResult.value.op_unmapped?.length > 0)
})

const loadStats = async () => {
  try {
    loading.value = true

    // 并行获取统计数据
    const [tpResult, opResult, actuatorResult] = await Promise.all([
      tpControlApi.getList({ page: 1, size: 1 }),
      opControlApi.getList({ page: 1, size: 1 }),
      actuatorMapApi.getList({ page: 1, size: 1 }),
    ])

    stats.value = {
      tpCount: tpResult.total,
      opCount: opResult.total,
      actuatorCount: actuatorResult.total,
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const validateConditions = async () => {
  try {
    validating.value = true
    const result = await codeGenerationApi.validateConditions()
    validationResult.value = result.data
    ElMessage.success('条件映射验证完成')
  } catch (error) {
    console.error('验证条件映射失败:', error)
    ElMessage.error('验证失败')
  } finally {
    validating.value = false
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 8px 0 0 0;
  color: var(--el-text-color-regular);
}

.stats-card {
  text-align: center;
  margin-bottom: 16px;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
}

.stats-label {
  color: var(--el-text-color-regular);
  margin-top: 8px;
}
</style>

<template>
  <div>
    <div class="page-header">
      <h1 class="page-title">OP控制逻辑管理</h1>
      <p class="page-description">管理操作流程(OP)控制逻辑数据</p>
    </div>

    <!-- 搜索筛选 -->
    <el-card style="margin-bottom: 16px;">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
            v-model="filters.step_id"
            placeholder="步骤ID"
            clearable
            type="number"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filters.em_description"
            placeholder="EM描述"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filters.em_variable"
            placeholder="EM变量"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-space>
            <el-button type="primary" :icon="Search" @click="handleSearch">
              搜索
            </el-button>
            <el-button :icon="Refresh" @click="handleReset">
              重置
            </el-button>
          </el-space>
        </el-col>
      </el-row>
    </el-card>

    <!-- 工具栏 -->
    <div class="table-toolbar">
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        新增OP记录
      </el-button>
      <el-button :icon="Refresh" @click="loadData">
        刷新
      </el-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <DataTable
        :data="data"
        :loading="loading"
        :table-layout="'auto'"
        :height="tableHeight"
      >
        <el-table-column prop="id" label="ID" width="80" sortable align="center" />
        <el-table-column prop="step_id" label="步骤ID" width="120" align="center" />
        <el-table-column prop="step_description" label="步骤描述" min-width="120" show-overflow-tooltip />
        <el-table-column prop="watchdog_mask" label="看门狗屏蔽" min-width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="action_description" label="动作描述" min-width="150" show-overflow-tooltip />
        <el-table-column prop="normal_interlock_condition" label="正常互锁条件" min-width="160" show-overflow-tooltip />
        <el-table-column prop="recovery_interlock_condition" label="恢复动作条件" min-width="160" show-overflow-tooltip />
        <el-table-column prop="recovery_interlock_action" label="恢复条件" min-width="160" show-overflow-tooltip />
        <el-table-column prop="skip_condition" label="跳步条件" min-width="160" show-overflow-tooltip />
        <el-table-column prop="alarm_condition" label="报警条件" min-width="120" show-overflow-tooltip />
        <el-table-column prop="em_description_cn" label="EM描述" min-width="150" show-overflow-tooltip />
        <el-table-column prop="op_sequence_under_em" label="OP序列" min-width="80" show-overflow-tooltip />
        <el-table-column prop="em_variable" label="EM变量" min-width="150" show-overflow-tooltip />

        <el-table-column prop="created_at" label="创建时间" width="160" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column prop="updated_at" label="更新时间" width="160" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatDate(row.updated_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="140" fixed="right" align="center">
          <template #default="{ row }">
            <el-space>
              <el-button type="primary" size="small" :icon="Edit" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-popconfirm
                title="确定要删除这条记录吗？"
                @confirm="handleDelete(row.id)"
              >
                <template #reference>
                  <el-button type="danger" size="small" :icon="Delete">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </el-space>
          </template>
        </el-table-column>
      </DataTable>
    </div>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 16px; justify-content: center;"
      @size-change="loadData"
      @current-change="loadData"
    />

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingRecord ? '编辑OP记录' : '新增OP记录'"
      width="900px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="步骤ID" prop="step_id">
              <el-input-number v-model="form.step_id" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="步骤描述" prop="step_description">
              <el-input v-model="form.step_description" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="看门狗掩码" prop="watchdog_mask">
              <el-select v-model="form.watchdog_mask" style="width: 100%">
                <el-option label="TRUE" value="TRUE" />
                <el-option label="FALSE" value="FALSE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="EM变量" prop="em_variable">
              <el-input v-model="form.em_variable" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="动作描述" prop="action_description">
          <el-input v-model="form.action_description" type="textarea" :rows="2" />
        </el-form-item>

        <el-form-item label="正常互锁条件" prop="normal_interlock_condition">
          <el-input v-model="form.normal_interlock_condition" type="textarea" :rows="2" />
        </el-form-item>

        <el-form-item label="恢复互锁条件" prop="recovery_interlock_condition">
          <el-input v-model="form.recovery_interlock_condition" type="textarea" :rows="2" />
        </el-form-item>

        <el-form-item label="报警条件" prop="alarm_condition">
          <el-input v-model="form.alarm_condition" type="textarea" :rows="2" />
        </el-form-item>

        <el-form-item label="跳步条件" prop="skip_condition">
          <el-input v-model="form.skip_condition" type="textarea" :rows="2" />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="EM描述(中文)" prop="em_description_cn">
              <el-input v-model="form.em_description_cn" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="OP序列" prop="op_sequence_under_em">
              <el-input v-model="form.op_sequence_under_em" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="恢复互锁动作" prop="recovery_interlock_action">
          <el-input v-model="form.recovery_interlock_action" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { opControlApi } from '@/services/api'
import type { OPControlLogic, OPControlLogicCreate, OPControlLogicUpdate } from '@/types'
import DataTable from '@/components/DataTable.vue'

const data = ref<OPControlLogic[]>([])
const loading = ref(false)
const dialogVisible = ref(false)
const editingRecord = ref<OPControlLogic | null>(null)
const formRef = ref()

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})

const filters = reactive({
  step_id: '',
  em_description: '',
  em_variable: '',
})

const form = reactive<OPControlLogicCreate>({
  step_id: 0,
  step_description: '',
  watchdog_mask: 'FALSE',
  action_description: '',
  normal_interlock_condition: '',
  recovery_interlock_condition: '',
  alarm_condition: '',
  skip_condition: '',
  em_description_cn: '',
  op_sequence_under_em: 'OP[1]',
  em_variable: '',
  recovery_interlock_action: '',
})

const rules = {
  step_id: [
    { required: true, message: '请输入步骤ID', trigger: 'blur' }
  ],
}

// 计算表格高度，让表格占据剩余空间
const tableHeight = computed(() => {
  // 页面头部 + 搜索卡片 + 工具栏 + 分页 + 边距
  const fixedHeight = 120 + 80 + 60 + 60 + 32
  return `calc(100vh - ${fixedHeight}px)`
})

const loadData = async () => {
  try {
    loading.value = true
    const params: any = {
      page: pagination.current,
      size: pagination.pageSize,
    }

    if (filters.step_id) params.step_id = Number(filters.step_id)
    if (filters.em_description) params.em_description = filters.em_description
    if (filters.em_variable) params.em_variable = filters.em_variable

    const result = await opControlApi.getList(params)
    data.value = result.items
    pagination.total = result.total
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(filters, {
    step_id: '',
    em_description: '',
    em_variable: '',
  })
  pagination.current = 1
  loadData()
}

const handleAdd = () => {
  editingRecord.value = null
  Object.assign(form, {
    step_id: 0,
    step_description: '',
    watchdog_mask: 'FALSE',
    action_description: '',
    normal_interlock_condition: '',
    recovery_interlock_condition: '',
    alarm_condition: '',
    skip_condition: '',
    em_description_cn: '',
    op_sequence_under_em: 'OP[1]',
    em_variable: '',
    recovery_interlock_action: '',
  })
  dialogVisible.value = true
}

const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

const handleEdit = (record: OPControlLogic) => {
  editingRecord.value = record
  Object.assign(form, record)
  dialogVisible.value = true
}

const handleDelete = async (id: number) => {
  try {
    await opControlApi.delete(id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (editingRecord.value) {
      await opControlApi.update(editingRecord.value.id, form as OPControlLogicUpdate)
      ElMessage.success('更新成功')
    } else {
      await opControlApi.create(form as OPControlLogicCreate)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('操作失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 8px 0 0 0;
  color: var(--el-text-color-regular);
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-container {
  height: calc(100vh - 320px);
  min-height: 400px;
  display: flex;
  flex-direction: column;
}
</style>

<template>
  <div>
    <div class="page-header">
      <h1 class="page-title">代码生成</h1>
      <p class="page-description">生成TP和OP的PLC代码，支持条件映射验证和代码下载</p>
    </div>

    <el-row :gutter="16">
      <!-- 左侧：生成配置 -->
      <el-col :xs="24" :lg="8">
        <el-card title="生成配置" style="height: 100%">
          <template #header>
            <span>生成配置</span>
          </template>

          <el-form
            :model="form"
            label-width="120px"
            style="margin-bottom: 20px;"
          >
            <el-form-item label="代码类型" required>
              <el-select v-model="form.table_type" style="width: 100%">
                <el-option label="TP控制逻辑" value="tp" />
                <el-option label="OP控制逻辑" value="op" />
              </el-select>
            </el-form-item>

            <el-form-item label="EM描述筛选">
              <el-input
                v-model="form.em_description"
                placeholder="例如: io_stEmBtryIn进电芯"
                clearable
              />
              <div class="form-help">可选，留空则生成所有数据的代码</div>
            </el-form-item>

            <el-form-item label="包含测试代码">
              <el-switch v-model="form.include_test" />
            </el-form-item>
          </el-form>

          <el-space direction="vertical" style="width: 100%">
            <el-button
              type="primary"
              :icon="VideoPlay"
              @click="generateCode"
              :loading="generating"
              style="width: 100%"
            >
              生成代码
            </el-button>

            <el-button
              :icon="Download"
              @click="downloadCode"
              :disabled="!generatedCode"
              style="width: 100%"
            >
              下载代码文件
            </el-button>

            <el-divider />

            <el-button
              :icon="CircleCheck"
              @click="validateConditions"
              :loading="validating"
              style="width: 100%"
            >
              验证条件映射
            </el-button>
          </el-space>
        </el-card>

        <!-- 验证结果 -->
        <el-card v-if="validationResult" style="margin-top: 16px;">
          <template #header>
            <span>验证结果</span>
          </template>

          <el-space direction="vertical" style="width: 100%">
            <el-alert
              v-if="validationResult.tp_unmapped?.length > 0"
              :title="`TP未映射条件: ${validationResult.tp_unmapped.length} 个`"
              type="warning"
              show-icon
            >
              <ul style="margin: 0; padding-left: 20px;">
                <li v-for="(condition, index) in validationResult.tp_unmapped.slice(0, 3)" :key="index">
                  <el-text type="info" tag="code">{{ condition }}</el-text>
                </li>
                <li v-if="validationResult.tp_unmapped.length > 3">
                  ... 还有 {{ validationResult.tp_unmapped.length - 3 }} 个
                </li>
              </ul>
            </el-alert>

            <el-alert
              v-if="validationResult.op_unmapped?.length > 0"
              :title="`OP未映射条件: ${validationResult.op_unmapped.length} 个`"
              type="warning"
              show-icon
            >
              <ul style="margin: 0; padding-left: 20px;">
                <li v-for="(condition, index) in validationResult.op_unmapped.slice(0, 3)" :key="index">
                  <el-text type="info" tag="code">{{ condition }}</el-text>
                </li>
                <li v-if="validationResult.op_unmapped.length > 3">
                  ... 还有 {{ validationResult.op_unmapped.length - 3 }} 个
                </li>
              </ul>
            </el-alert>

            <el-alert
              v-if="!hasUnmappedConditions"
              title="所有条件映射完整"
              description="当前系统中的所有条件都已正确映射。"
              type="success"
              show-icon
            />
          </el-space>
        </el-card>
      </el-col>

      <!-- 右侧：代码显示 -->
      <el-col :xs="24" :lg="16">
        <el-card style="height: 100%">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <el-space>
                <el-icon><DocumentCopy /></el-icon>
                <span>生成的代码</span>
                <el-text v-if="generatedCode" type="info">({{ generatedCode.filename }})</el-text>
              </el-space>
              <el-button
                v-if="generatedCode"
                type="primary"
                :icon="CopyDocument"
                @click="copyCode"
                :loading="copying"
                size="small"
              >
                复制代码
              </el-button>
            </div>
          </template>

          <div v-if="generatedCode">
            <!-- 警告信息 -->
            <el-alert
              v-if="generatedCode.warnings.length > 0"
              title="生成警告"
              type="warning"
              show-icon
              style="margin-bottom: 16px"
            >
              <ul style="margin: 0; padding-left: 20px;">
                <li v-for="(warning, index) in generatedCode.warnings" :key="index">
                  {{ warning }}
                </li>
              </ul>
            </el-alert>

            <!-- 未映射条件 -->
            <el-alert
              v-if="generatedCode.unmapped_conditions.length > 0"
              :title="`发现 ${generatedCode.unmapped_conditions.length} 个未映射条件`"
              type="error"
              show-icon
              style="margin-bottom: 16px"
            >
              <ul style="margin: 0; padding-left: 20px;">
                <li v-for="(condition, index) in generatedCode.unmapped_conditions.slice(0, 5)" :key="index">
                  <el-text type="info" tag="code">{{ condition }}</el-text>
                </li>
                <li v-if="generatedCode.unmapped_conditions.length > 5">
                  ... 还有 {{ generatedCode.unmapped_conditions.length - 5 }} 个
                </li>
              </ul>
            </el-alert>

            <!-- 代码编辑器 -->
            <div class="code-editor">
              <el-input
                v-model="generatedCode.code"
                type="textarea"
                :rows="25"
                readonly
                style="font-family: 'Courier New', monospace; font-size: 14px;"
              />
            </div>
          </div>

          <div v-else class="empty-state">
            <el-empty description="请配置参数并点击生成代码按钮">
              <template #image>
                <el-icon size="64" color="#d9d9d9"><DocumentCopy /></el-icon>
              </template>
            </el-empty>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  VideoPlay,
  Download,
  CircleCheck,
  DocumentCopy,
  CopyDocument
} from '@element-plus/icons-vue'
import { codeGenerationApi } from '@/services/api'
import type { CodeGenerationRequest, CodeGenerationResponse } from '@/types'

const form = reactive<CodeGenerationRequest>({
  table_type: 'tp',
  em_description: '',
  include_test: false,
})

const generating = ref(false)
const validating = ref(false)
const copying = ref(false)
const generatedCode = ref<CodeGenerationResponse | null>(null)
const validationResult = ref<any>(null)

const hasUnmappedConditions = computed(() => {
  return validationResult.value &&
    (validationResult.value.tp_unmapped?.length > 0 || validationResult.value.op_unmapped?.length > 0)
})

const generateCode = async () => {
  try {
    generating.value = true

    let result: CodeGenerationResponse
    if (form.table_type === 'tp') {
      result = await codeGenerationApi.generateTP(form)
    } else {
      result = await codeGenerationApi.generateOP(form)
    }

    generatedCode.value = result
    ElMessage.success('代码生成成功')
  } catch (error) {
    ElMessage.error('代码生成失败')
    console.error('代码生成错误:', error)
  } finally {
    generating.value = false
  }
}

const downloadCode = () => {
  if (!generatedCode.value) return

  const blob = new Blob([generatedCode.value.code], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = generatedCode.value.filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('文件下载成功')
}

const copyCode = async () => {
  if (!generatedCode.value) return

  try {
    copying.value = true
    await navigator.clipboard.writeText(generatedCode.value.code)
    ElMessage.success('代码已复制到剪贴板')
  } catch (error) {
    // 如果 Clipboard API 不可用，使用传统方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = generatedCode.value.code
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('代码已复制到剪贴板')
    } catch (fallbackError) {
      ElMessage.error('复制失败，请手动选择复制')
      console.error('复制错误:', fallbackError)
    }
  } finally {
    copying.value = false
  }
}

const validateConditions = async () => {
  try {
    validating.value = true
    const result = await codeGenerationApi.validateConditions()
    validationResult.value = result.data
    ElMessage.success('条件映射验证完成')
  } catch (error) {
    ElMessage.error('验证失败')
    console.error('验证错误:', error)
  } finally {
    validating.value = false
  }
}
</script>

<style scoped>
.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 8px 0 0 0;
  color: var(--el-text-color-regular);
}

.form-help {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
}

.code-editor {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.empty-state {
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-fill-color-lighter);
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
}
</style>

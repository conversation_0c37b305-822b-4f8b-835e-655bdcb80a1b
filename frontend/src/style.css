/* Vue + Element Plus 样式 */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#app {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

/* 自定义样式 */
.app-layout {
  min-height: 100vh;
}

.app-header {
  background: var(--el-color-primary);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.app-logo {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.app-content {
  margin: 24px;
  padding: 24px;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
}

.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 8px 0 0 0;
  color: var(--el-text-color-regular);
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code-editor {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.stats-card {
  text-align: center;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
}

.stats-label {
  color: var(--el-text-color-regular);
  margin-top: 8px;
}

/* 表格样式优化 */
.el-table {
  /* 确保表格布局稳定 */
  table-layout: auto !important;
}

.el-table .el-table__header-wrapper {
  /* 表头样式 */
  overflow: hidden;
}

.el-table .el-table__body-wrapper {
  /* 表体样式 */
  overflow-x: auto;
  overflow-y: auto;
}

/* 修复表头和数据对齐问题 */
.el-table--border .el-table__header th,
.el-table--border .el-table__body td {
  border-right: 1px solid var(--el-table-border-color);
}

/* 表格滚动条样式 */
.el-table .el-scrollbar__bar {
  opacity: 1;
}

.el-table .el-scrollbar__thumb {
  background-color: var(--el-color-info-light-7);
}

.el-table .el-scrollbar__thumb:hover {
  background-color: var(--el-color-info-light-5);
}

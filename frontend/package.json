{"name": "plc-management-frontend", "version": "1.0.0", "description": "PLC Auto Coding System - Frontend Management Interface", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:host": "vite --host 0.0.0.0", "build": "vite build", "build:check": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@monaco-editor/loader": "^1.4.0", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.45.0", "@vue-flow/minimap": "^1.5.3", "@vue-flow/node-toolbar": "^1.1.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "monaco-editor": "^0.45.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.2", "sass": "^1.69.5", "typescript": "^5.3.2", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.8", "vue-tsc": "^2.0.0"}}
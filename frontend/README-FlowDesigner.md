# 流程设计器使用说明

## 功能概述

流程设计器是一个基于Vue Flow的可视化流程编辑工具，支持创建、编辑和管理流程节点，每个节点都可以配置名称、条件和动作等属性。现在支持与数据库的TP流程数据进行交互。

## 主要功能

### 1. 节点管理
- **添加节点**: 点击工具栏的"添加节点"按钮创建新节点（自动连接到上一个节点）
- **删除节点**: 选中节点后点击工具栏的"删除节点"按钮，或使用节点工具栏的删除按钮
- **选择节点**: 点击节点进行选择，选中的节点会高亮显示
- **智能排列**: 点击"自动排列"按钮应用智能布局算法，自动优化节点位置
- **载入TP流程**: 点击"载入TP流程"按钮从数据库载入现有的TP流程
- **保存TP流程**: 点击"保存TP流程"按钮将当前流程图保存到数据库
- **测试布局**: 点击"测试布局"按钮创建一个复杂的测试流程，展示智能布局效果

### 2. 节点属性编辑
右侧属性面板提供以下编辑功能：

#### 基本信息
- **节点名称**: 设置节点的显示名称（对应step_description）
- **条件**: 设置节点的执行条件（对应motion_condition），支持多个条件用分号分隔
- **动作**: 设置节点要执行的动作（对应motion_action）

#### 显示设置
- **工具栏位置**: 选择节点工具栏的显示位置（顶部/右侧/底部/左侧）
- **工具栏显示**: 选择工具栏是始终显示还是仅在悬停时显示

### 3. 连接线管理
- **选择连接线**: 点击连接线进行选择，选中的连接线会高亮显示
- **删除连接线**: 选中连接线后点击工具栏的"删除连接线"按钮
- **编辑跳转条件**: 在右侧属性面板中编辑连接线的跳转条件（对应skip_condition）
- **条件标签**: 连接线上会显示跳转条件标签（如果设置了条件）

### 4. TP流程数据交互

#### 载入TP流程
1. 点击"载入TP流程"按钮打开载入对话框
2. 对话框显示数据库中按EM描述分组的所有TP流程
3. 每个流程组显示前3个步骤的预览信息
4. 选择要载入的流程，点击"载入流程"按钮
5. 系统会自动将TP数据转换为流程图节点和连接线

#### 保存TP流程
1. 点击"保存TP流程"按钮打开保存对话框
2. 填写必要的流程信息：
   - **EM描述**: TP流程的中文描述
   - **EM变量**: 如io_stEmBtryIn（支持模板快速插入）
   - **OP序列**: 如OP[1]（支持模板快速插入）
   - **TP序列**: 如io_stUm.TP[1]
3. 预览表格显示将要保存的数据结构
4. 点击"保存流程"按钮，系统会：
   - 先删除数据库中相同EM和OP的现有数据
   - 将流程图转换为TP数据格式并保存到数据库

### 5. 数据映射规则

#### 节点到TP数据的映射
- **step_id**: 按节点顺序生成（0, 10, 20, 30...）
- **step_description**: 节点名称
- **motion_condition**: 节点条件
- **motion_action**: 节点动作
- **skip_condition**: 从该节点出发的连接线条件（多个用分号分隔）
- **skip_step**: 跳转目标节点的step_id（多个用分号分隔）
- **tp_sequence**: 用户输入的TP序列
- **em_description_cn**: 用户输入的EM描述
- **em_variable**: 用户输入的EM变量
- **op_sequence_under_em**: 用户输入的OP序列

#### TP数据到节点的映射
- 按step_id排序创建节点
- 解析skip_condition和skip_step创建跳转连接线
- 为没有跳转的节点创建默认的顺序连接线

### 6. 模板功能
属性面板提供常用的条件和动作模板：

#### 常用条件模板
- `SV1在位置1`, `SV2在位置2` - 阀门位置条件
- `YV1在开位`, `YV2在关位` - 阀门状态条件
- `bDoneAction[1]`, `bDoneAction[2]` - 完成动作条件
- `bCmdAction[1]` - 命令动作条件
- `bAlm[10]` - 报警条件

#### 常用动作模板
- `启动电机`, `停止电机` - 电机控制
- `打开阀门`, `关闭阀门` - 阀门控制
- `检查状态`, `发送信号` - 状态检查
- `等待完成`, `报警处理` - 流程控制

#### 常用跳转条件模板
- `成功`, `失败` - 基本执行结果
- `超时`, `异常` - 异常情况处理
- `完成`, `跳过` - 流程控制
- `重试`, `继续` - 循环控制

#### EM变量和OP序列模板
- **EM变量**: io_stEmBtryIn, io_stEmBtryOut, io_stEmBtryMove等
- **OP序列**: OP[1], OP[2], OP[3]等

### 7. 实时更新
- **实时更新模式**: 开启后，属性修改会立即应用到节点和连接线
- **手动更新模式**: 关闭后，需要点击"应用更改"按钮才会更新节点或连接线

## 操作指南

### 创建新流程
1. 点击"添加节点"按钮创建节点（新节点会自动连接到选中节点）
2. 点击节点选中它，在右侧属性面板编辑节点信息
3. 通过拖拽连接点创建自定义连接线（从上方连接点拖到下方连接点）
4. 点击连接线选中它，在右侧属性面板编辑跳转条件
5. 点击"自动排列"按钮应用智能布局，自动优化节点位置和间距

### 载入现有流程
1. 点击"载入TP流程"按钮
2. 在对话框中选择要载入的EM流程
3. 点击"载入流程"按钮
4. 系统自动转换数据并显示流程图

### 保存流程到数据库
1. 创建或编辑完流程图后，点击"保存TP流程"按钮
2. 填写EM描述、EM变量、OP序列、TP序列等信息
3. 预览生成的数据结构
4. 点击"保存流程"按钮完成保存

### 编辑节点和连接线
1. 点击选中要编辑的节点或连接线
2. 在右侧属性面板修改相应属性
3. 对于节点：使用模板按钮快速插入常用条件和动作
4. 对于连接线：使用模板按钮快速插入常用跳转条件
5. 如果开启了实时更新，修改会立即生效

## 技术特性

### 智能布局算法
- **层级分析**: 自动分析节点之间的依赖关系，按层级排列
- **多分支处理**: 当一个节点有多个跳转目标时，目标节点会横向排列
- **空间优化**: 自动计算节点间距，避免重叠，预留合适的空间
- **起始节点识别**: 自动识别没有入边的节点作为起始节点
- **循环处理**: 能够处理复杂的循环引用关系

### 连接功能
- **纵向布局**: 节点采用从上到下的纵向布局，连接点位于节点的顶部和底部
- **箭头连接线**: 所有连接线都带有箭头，清晰显示流程方向
- **拖拽连接**: 可以通过拖拽连接点创建自定义连接线
- **自动连接**: 新添加的节点会自动连接到选中节点或最后一个节点

### 数据库集成
- **实时数据**: 载入对话框显示数据库中的实时TP流程数据
- **批量操作**: 支持批量删除和创建TP流程数据
- **数据验证**: 保存前会验证必要字段的完整性
- **冲突处理**: 保存时会先删除现有数据，避免冲突

### 组件架构
- **FlowChart.vue**: 主流程图组件，管理节点和连接线
- **CustomNode.vue**: 自定义节点组件，显示节点内容和工具栏
- **PropertiesPanel.vue**: 属性面板组件，提供节点和连接线编辑功能
- **LoadTPFlowDialog.vue**: 载入TP流程对话框组件
- **SaveTPFlowDialog.vue**: 保存TP流程对话框组件

### 数据结构
```typescript
interface NodeData {
  name: string          // 节点名称 -> step_description
  condition: string     // 执行条件 -> motion_condition
  action: string        // 执行动作 -> motion_action
}

interface EdgeData {
  condition: string     // 跳转条件 -> skip_condition
}
```

## 注意事项

1. **数据备份**: 保存流程时会删除现有数据，请确保重要数据已备份
2. **字段限制**: 各字段有长度限制，超长内容会被截断
3. **网络连接**: 载入和保存功能需要后端服务器正常运行
4. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验

现在你的流程设计器支持完整的TP流程数据交互功能，可以方便地在可视化界面和数据库数据之间进行转换！

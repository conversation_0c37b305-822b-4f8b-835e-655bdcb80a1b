import pymysql  # type: ignore
from typing import Dict, List, Optional, Set
import re


class OPCodeGenerator:
    def __init__(self, db_config: Optional[Dict] = None):
        self.step_data: List[Dict] = []
        self.actuator_data: List[Dict] = []
        self.db_config = db_config
        self.connection = None
        self.unmapped_conditions: Set = set()

    @staticmethod
    def safe_get_string(data_dict: Dict, key: str, default: str = "") -> str:
        """安全获取字符串值，处理None和空值"""
        value = data_dict.get(key)
        if value is None:
            return default
        if isinstance(value, str):
            return value.strip()
        return str(value).strip()

    @staticmethod
    def safe_get_int(data_dict: Dict, key: str, default: int = 0) -> int:
        """安全获取整数值，处理None和转换错误"""
        value = data_dict.get(key)
        if value is None:
            return default
        try:
            return int(value)
        except (ValueError, TypeError):
            return default

    def get_em_variable(self, step: Dict) -> str:
        """获取EM变量名"""
        return self.safe_get_string(step, "em_variable", "io_stEmBtryIn")

    def get_op_sequence(self, step: Dict) -> str:
        """获取完整的OP序列变量路径"""
        em_variable = self.safe_get_string(step, "em_variable", "io_stEmBtryIn")
        op_sequence = self.safe_get_string(step, "op_sequence_under_em", "OP[1]")

        # 确保大小写正确
        if op_sequence.lower() == "op[1]":
            op_sequence = "OP[1]"

        if op_sequence:
            return f"{em_variable}.{op_sequence}"
        else:
            return f"{em_variable}.OP[1]"

    def connect_database(self, db_config: Optional[Dict] = None):
        """连接MySQL数据库"""
        if db_config:
            self.db_config = db_config

        if not self.db_config:
            raise ValueError("数据库配置信息不能为空")

        try:
            self.connection = pymysql.connect(
                host=self.db_config.get("host", "localhost"),
                port=self.db_config.get("port", 3306),
                user=self.db_config.get("user"),
                password=self.db_config.get("password"),
                database=self.db_config.get("database"),
                charset=self.db_config.get("charset", "utf8mb4"),
                autocommit=True,
            )
            print("数据库连接成功!")
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False

    def disconnect_database(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            print("数据库连接已关闭")

    def load_op_data_from_db(self, table_name: str = "op_control_logic"):
        """从数据库加载OP步骤数据，按id排序"""
        if not self.connection:
            raise ConnectionError("请先连接数据库")

        try:
            query = f"""
            SELECT id, step_id, step_description, watchdog_mask, action_description,
                   normal_interlock_condition, recovery_interlock_condition,
                   alarm_condition, skip_condition, em_description_cn, op_sequence_under_em,
                   em_variable, recovery_interlock_action
            FROM {table_name}
            ORDER BY id
            """

            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute(query)
            self.step_data = cursor.fetchall()
            cursor.close()

            print(f"成功加载 {len(self.step_data)} 条OP步骤数据")
            return self.step_data

        except Exception as e:
            print(f"加载OP步骤数据失败: {e}")
            return []

    def load_actuator_data_from_db(self, table_name: str = "actuator_variable_map"):
        """从数据库加载执行器映射数据"""
        if not self.connection:
            raise ConnectionError("请先连接数据库")

        try:
            query = f"""
            SELECT parent_struct, actuator, command_variable, command_description_cn
            FROM {table_name}
            ORDER BY parent_struct, actuator, command_variable
            """

            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute(query)
            self.actuator_data = cursor.fetchall()
            cursor.close()

            print(f"成功加载 {len(self.actuator_data)} 条执行器映射数据")
            return self.actuator_data

        except Exception as e:
            print(f"加载执行器映射数据失败: {e}")
            return []

    def load_all_data_from_db(
        self,
        op_table: str = "op_control_logic",
        actuator_table: str = "actuator_variable_map",
    ):
        """从数据库加载所有数据"""
        self.load_op_data_from_db(op_table)
        self.load_actuator_data_from_db(actuator_table)
        return len(self.step_data) > 0 and len(self.actuator_data) > 0

    def extract_actuator_name(self, text: str) -> str:
        """从文本中提取执行器名称（如SV1、YV2等）"""
        # 匹配字母+数字的组合
        match = re.search(r"([A-Za-z]+)(\d+)", text)
        if match:
            letters = match.group(1)
            numbers = match.group(2)
            return f"{letters}[{numbers}]"
        return text

    def extract_action_name(self, action_text: str) -> str:
        """从动作文本中提取动作名称部分"""
        # 移除执行器名称，保留动作部分
        # 例如："SV1回零" -> "回零", "YV1去缩位" -> "去缩位"
        match = re.search(r"[A-Za-z]+\d+(.+)", action_text)
        if match:
            return match.group(1).strip()
        return action_text.strip()

    def extract_condition_name(self, condition_text: str) -> str:
        """从条件文本中提取条件名称部分"""
        # 处理条件文本，如"SV1不在零点" -> "不在零点"
        match = re.search(r"[A-Za-z]+\d+(.+)", condition_text)
        if match:
            return match.group(1).strip()
        return condition_text.strip()

    def build_condition_mappings(self) -> Dict[str, Dict[str, str]]:
        """构建条件映射字典"""
        condition_mappings = {}

        for actuator in self.actuator_data:
            description = self.safe_get_string(actuator, "command_description_cn")
            # if description:
            #     parent_struct = self.safe_get_string(actuator, "parent_struct")
            #     actuator_name = self.safe_get_string(actuator, "actuator")
            #     command_var = self.safe_get_string(actuator, "command_variable")

            #     # 构建完整的PLC变量路径
            #     if actuator_name:
            #         # 如果有执行器名称，构建包含执行器的路径
            #         plc_variable = f"{parent_struct}.{actuator_name}.{command_var}"
            #     elif parent_struct and command_var:
            #         # 如果没有执行器名称，直接使用parent_struct + command_variable
            #         plc_variable = f"{parent_struct}.{command_var}"
            #     else:
            #         plc_variable = command_var

            condition_mappings[description] = actuator

        return condition_mappings

    def _build_variable_path(
        self, var_mapping: Dict[str, str], step: Dict, condition_text: str = ""
    ) -> Optional[str]:
        """构建变量路径，处理parent_struct和actuator的Y/N/OP逻辑"""
        parent_struct = var_mapping.get("parent_struct", "")
        actuator_flag = var_mapping.get("actuator", "")
        command_variable = var_mapping.get("command_variable", "")

        # 构建基础路径
        path_parts = []

        # 处理parent_struct：Y表示需要动态拼接EM变量，N表示使用原值
        if parent_struct == "Y":
            # 动态拼接EM变量
            em_variable = self.safe_get_string(step, "em_variable", "io_stEmBtryIn")
            path_parts.append(em_variable)
        elif parent_struct == "N":
            # 不拼接parent_struct
            pass
        elif parent_struct:  # 如果是具体的值，直接使用
            path_parts.append(parent_struct)

        # 处理actuator：Y表示动态检测执行器，N表示不拼接，OP表示拼接OP[N]
        if actuator_flag == "Y":
            # 动态检测执行器并拼接
            actuator_match = re.search(r"([A-Za-z]+\d+)", condition_text)
            if actuator_match:
                actuator_part = actuator_match.group(1)
                formatted_actuator = self.extract_actuator_name(actuator_part)
                path_parts.append(formatted_actuator)
            else:
                return None  # 没有找到执行器，无法构建路径
        elif actuator_flag == "OP":
            # 拼接OP[N]变量
            op_sequence = self.safe_get_string(step, "op_sequence_under_em", "OP[1]")
            path_parts.append(op_sequence)
        elif actuator_flag == "N":
            # 不拼接actuator
            pass

        # 添加command_variable
        if command_variable:
            path_parts.append(command_variable)

        # 组合路径
        if path_parts:
            return ".".join(path_parts)
        else:
            return command_variable if command_variable else None

    def find_matching_condition(
        self, condition_text: str, mappings: Dict[str, Dict[str, str]], step: Dict
    ) -> Optional[str]:
        """在映射表中查找匹配的条件"""
        # 1. 直接匹配
        if condition_text in mappings:
            return self._build_variable_path(
                mappings[condition_text], step, condition_text
            )

        # 2. 处理正则表达式模式匹配（通用处理）
        for desc, var_path in mappings.items():
            # 检查是否是正则表达式模式（包含反斜杠转义字符）
            if "\\" in desc and "(" in desc and ")" in desc:
                try:
                    # 将数据库中的正则表达式转换为Python正则表达式
                    regex_pattern = desc.replace("\\d", r"\d").replace("\\+", "+")

                    # 尝试匹配整个条件文本
                    full_match = re.search(regex_pattern, condition_text)
                    if full_match and len(full_match.groups()) > 0:
                        # 提取数字
                        number = full_match.group(1)

                        # 创建临时映射，替换变量中的N为实际数字
                        temp_mapping = var_path.copy()
                        temp_mapping["command_variable"] = var_path[
                            "command_variable"
                        ].replace("[N]", f"[{number}]")

                        # 使用统一的路径构建方法
                        result = self._build_variable_path(
                            temp_mapping, step, condition_text
                        )
                        if result:
                            return result
                except re.error:
                    # 如果正则表达式有问题，跳过
                    continue

        # 3. 提取执行器和条件部分进行匹配
        actuator_match = re.search(r"([A-Za-z]+\d+)(.+)", condition_text)
        if actuator_match:
            condition_part = actuator_match.group(2).strip()  # 如 "在零点"

            # 在映射表中查找条件部分
            for desc, var_path in mappings.items():
                # 简单关键词匹配
                if condition_part in desc:
                    # 使用统一的路径构建方法
                    result = self._build_variable_path(var_path, step, condition_text)
                    if result:
                        return result

        # 如果找不到匹配，返回None
        return None

    def parse_condition(self, condition_text: str, step: Dict) -> str:
        """解析条件文本为PLC代码"""
        condition_text = self.safe_get_string({"text": condition_text}, "text")

        if not condition_text:
            return "TRUE"

        if condition_text in ["TRUE", "FALSE"]:
            return condition_text

        # 构建条件映射
        condition_mappings = self.build_condition_mappings()

        # 处理复合条件（包含逻辑运算符）
        if (
            " AND " in condition_text
            or " OR " in condition_text
            or condition_text.startswith("NOT ")
        ):
            return self._parse_complex_condition(
                condition_text, condition_mappings, step
            )

        # 查找匹配的条件
        result = self.find_matching_condition(condition_text, condition_mappings, step)

        if result:
            return result

        # 如果找不到匹配，记录未映射条件
        self.unmapped_conditions.add(condition_text)
        return f"/* 未映射: {condition_text} */"

    def _parse_complex_condition(
        self, condition_text: str, mappings: Dict[str, Dict[str, str]], step: Dict
    ) -> str:
        """解析复合条件"""
        # 处理 NOT（优先级最高）
        if condition_text.startswith("NOT "):
            base_condition = condition_text[4:].strip()
            mapped_base = self.parse_condition(base_condition, step)
            return f"NOT {mapped_base}"

        # 处理 OR（优先级最低）
        if " OR " in condition_text:
            or_parts = condition_text.split(" OR ")
            mapped_parts = []
            for part in or_parts:
                part = part.strip()
                mapped_part = self.parse_condition(part, step)
                mapped_parts.append(mapped_part)
            return " OR ".join(mapped_parts)

        # 处理 AND
        if " AND " in condition_text:
            and_parts = condition_text.split(" AND ")
            mapped_parts = []
            for part in and_parts:
                part = part.strip()
                mapped_part = self.parse_condition(part, step)
                mapped_parts.append(mapped_part)
            return " AND ".join(mapped_parts)

        # 如果没有复合操作符，直接递归调用单条件解析
        return self.parse_condition(condition_text, step)

    def group_steps_by_em_description(self) -> Dict[str, List[Dict]]:
        """按EM描述分组步骤数据"""
        em_groups: Dict[str, List[Dict]] = {}

        for step in self.step_data:
            em_desc = self.safe_get_string(step, "em_description_cn", "默认EM")

            if em_desc not in em_groups:
                em_groups[em_desc] = []
            em_groups[em_desc].append(step)

        return em_groups

    def group_steps_by_action(
        self, steps_data: Optional[List[Dict]] = None
    ) -> Dict[int, List[Dict]]:
        """按Action分组步骤（基于step_id=0作为分组标识）"""
        if steps_data is None:
            steps_data = self.step_data

        action_groups: Dict[int, List[Dict]] = {}
        current_action = 0

        for step in steps_data:
            step_id = self.safe_get_int(step, "step_id", 0)
            step_desc = self.safe_get_string(step, "step_description")

            # 当遇到step_id=0时，开始新的Action组
            if step_id == 0:
                # 从描述中提取Action号
                action_match = re.search(r"Action(\d+)", step_desc)
                if action_match:
                    current_action = int(action_match.group(1))
                else:
                    current_action += 1

            # 将步骤添加到当前Action组
            if current_action not in action_groups:
                action_groups[current_action] = []
            action_groups[current_action].append(step)

        return action_groups

    def calculate_max_actions(self, steps_data: List[Dict]) -> int:
        """动态计算EM模块需要的最大Action数量"""
        if not steps_data:
            return 14  # 默认值

        action_groups = self.group_steps_by_action(steps_data)
        max_action_num = max(action_groups.keys()) if action_groups else 1

        # 确保至少有14个，但如果实际需要更多则使用实际数量
        return max_action_num

    def generate_em_command_conversion(
        self, steps_data: List[Dict], max_actions: Optional[int] = None
    ) -> str:
        """生成EM命令号转换代码"""
        if not steps_data:
            return ""

        # 动态计算最大Action数量
        if max_actions is None:
            max_actions = self.calculate_max_actions(steps_data)

        # 从第一条记录获取变量信息
        first_step = steps_data[0]
        op_sequence = self.safe_get_string(first_step, "op_sequence_under_em", "OP[1]")
        em_variable = self.safe_get_string(first_step, "em_variable", "io_stEmBtryIn")
        em_description = self.safe_get_string(first_step, "em_description_cn")
        # # 安全解析OP序列号
        # try:
        #     op_sequence_num = int(op_sequence.replace("OP[", "").replace("]", ""))
        # except ValueError:
        #     # 如果解析失败，默认使用1
        #     # op_sequence_num = 1
        #     print(f"⚠️  警告：无法解析OP序列号 '{op_sequence}'，使用默认值1")

        # 生成动态函数块名称
        fb_name = self.generate_fb_name(em_variable, op_sequence)

        code_lines = []
        code_lines.append(
            f"//=================================={em_description}=============================="
        )
        code_lines.append(f"""{fb_name}(\
                          \n\tio_stBase:={em_variable}.Base,\
                          \n\tio_stCmdSta:={em_variable}.{op_sequence}.CmdSta,\
                          \n\tio_stOP:={em_variable}.{op_sequence},\
                          \n\ti_bError:={em_variable}.Base.bError,\
                          \n\to_rWatchDogTime=>arrWatchDogTime[1]\
                          \n\t);\n""")

        code_lines.append(
            "//==================================EM命令号转换=============================="
        )
        code_lines.append("IF TRUE THEN")
        code_lines.append("\t//命令触发\t")

        for i in range(1, max_actions + 1):
            if i == 1:
                code_lines.append(
                    f"\tIF {em_variable}.{op_sequence}.bCmdAction[{i}] AND NOT {em_variable}.{op_sequence}.bDoneAction[{i}] THEN"
                )
            else:
                code_lines.append(
                    f"\tELSIF {em_variable}.{op_sequence}.bCmdAction[{i}] AND NOT {em_variable}.{op_sequence}.bDoneAction[{i}] THEN"
                )
            code_lines.append(
                f"\t\t{em_variable}.{op_sequence}.CmdSta.iCmdNoReq:={i + 1};"
            )

        code_lines.append("\tEND_IF;")
        code_lines.append("END_IF;")
        code_lines.append("")

        return "\n".join(code_lines)

    def generate_fb_name(self, em_variable: str, op_sequence: str) -> str:
        """动态生成函数块名称"""
        # 从EM变量名中提取关键部分
        # 例如：io_stEmBtryIn -> BtryIn, io_stEmBtryMove -> BtryMove, io_stEmTrayLift -> TrayLift
        if "stEm" in em_variable:
            em_part = em_variable.split("stEm")[-1]  # 获取stEm后面的部分
        else:
            em_part = em_variable.replace("io_st", "").replace("Em", "")

        # 从OP序列中提取数字
        # 例如：OP[1] -> Op1, op[1] -> Op1
        op_num = op_sequence.replace("OP[", "").replace("op[", "").replace("]", "")

        return f"fb{em_part}Op{op_num}"

    def generate_em_state_machine(self, steps_data: List[Dict]) -> str:
        """生成EM状态机控制代码"""
        if not steps_data:
            return ""

        first_step = steps_data[0]
        op_sequence_under_em = self.safe_get_string(
            first_step, "op_sequence_under_em", "OP[1]"
        )
        em_variable = self.safe_get_string(first_step, "em_variable", "io_stEmBtryIn")
        # em_name = em_variable.split(".")[0] if "." in em_variable else "io_stEmBtryIn"

        code_lines = []
        code_lines.append(
            "//==================================EM状态机控制========================="
        )
        code_lines.append("IF TRUE THEN")
        code_lines.append("\tfbSubUnitSC(")
        code_lines.append(f"\t\tio_stBase:={em_variable}.Base, ")
        code_lines.append("\t\tio_stBaseParent:=io_stUm.Base,")
        code_lines.append(
            f"\t\tio_stCmdSta:={em_variable}.{op_sequence_under_em}.CmdSta, "
        )
        code_lines.append("\t\ti_tTimeout:=tTimeout,")
        code_lines.append("\t\to_bActive=>o_stEmMonitor.Status.bActive,")
        code_lines.append("\t\to_bBusy=>o_stEmMonitor.Status.bBusy,")
        code_lines.append("\t\to_bHomeOK=>o_stEmMonitor.Status.bHomeOK,")
        code_lines.append("\t\to_bOneCycleMode=>o_stEmMonitor.Cmd.bOneCycleMode,")
        code_lines.append("\t\to_bStateComplete=>o_stEmMonitor.Status.bStateComplete")
        code_lines.append("\t\t);")
        code_lines.append("\t")
        code_lines.append("END_IF;")
        code_lines.append("")

        return "\n".join(code_lines)

    def parse_action_descriptions(self, action_desc: str) -> List[str]:
        """解析动作描述（按分号分隔）"""
        if not action_desc:
            return []
        return [desc.strip() for desc in action_desc.split(";") if desc.strip()]

    def parse_conditions(self, condition_text: str) -> List[str]:
        """解析条件文本（按分号分隔）"""
        if not condition_text:
            return []
        return [cond.strip() for cond in condition_text.split(";") if cond.strip()]

    def parse_recovery_action_descriptions(self, action_desc: str) -> List[List[str]]:
        """解析动作描述（按分号分隔）"""
        if not action_desc:
            return []
        return [desc.strip().split(",") for desc in action_desc.split(";")]

    def parse_recovery_conditions(self, condition_text: str) -> List[List[str]]:
        """解析条件文本（按分号分隔）"""
        if not condition_text:
            return []
        return [cond.strip().split(",") for cond in condition_text.split(";")]

    def generate_action_process_code(self, action_num: int, steps: List[Dict]) -> str:
        """生成单个Action流程代码"""
        if not steps:
            return ""

        first_step = steps[0]
        em_variable = self.get_op_sequence(first_step)
        em_name = self.get_em_variable(first_step)
        em_description = self.safe_get_string(first_step, "em_description_cn")
        op_sequence = self.safe_get_string(first_step, "op_sequence_under_em", "OP[1]")

        # 生成动态函数块名称
        fb_name = self.generate_fb_name(em_name, op_sequence)

        cmd_no = action_num + 1  # 命令号比Action号大1

        code_lines = []
        code_lines.append(
            f"//================================={em_description}================================="
        )
        code_lines.append(f"IF {em_variable}.CmdSta.iCmdNo={cmd_no} THEN")
        code_lines.append(f"\tCASE {em_variable}.iCmdStep OF")

        for i, step in enumerate(steps):
            step_id = self.safe_get_int(step, "step_id", 0)
            step_desc = self.safe_get_string(step, "step_description")

            if step_id == 0:
                # 等待Action运行信号的特殊处理
                code_lines.append(f"\t\t {step_id}://等待Action运行信号")
                code_lines.append(f"\t\t\tIF {em_variable}.bCmdRun THEN\t")
                code_lines.append(
                    f"\t\t \t\tIF  {em_variable}.bCmdAction[{action_num}] THEN"
                )

                # 找到下一个非0步骤
                next_step = None
                for next_s in steps:
                    next_step_id = self.safe_get_int(next_s, "step_id", 0)
                    if next_step_id > 0:
                        next_step = next_step_id
                        break
                if next_step:
                    code_lines.append(
                        f"\t\t \t\t\t {em_variable}.iCmdStep:={next_step};"
                    )
                code_lines.append("\t\t\t\tEND_IF;")
                code_lines.append("\t\t\tEND_IF;")

            else:
                # 普通步骤处理
                code_lines.append("\t\t")
                code_lines.append(
                    f"\t\t{step_id}:{fb_name}.i_strStepInfo1:='{step_desc}';"
                )
                code_lines.append(f"\t\t \t{fb_name}.i_strStepInfo2:='';")

                # 添加watchdog控制
                watchdog = self.safe_get_string(step, "watchdog_mask")
                if watchdog.upper() in ["TRUE", "FALSE"]:
                    code_lines.append(
                        f"\t\t\t{em_variable}.bDisableTimeCheck:={watchdog.upper()};"
                    )

                # 添加运动逻辑
                self._add_motion_logic(code_lines, step, em_variable, em_name)

                # 添加报警逻辑
                self._add_alarm_logic(code_lines, step, em_variable)

                # 添加跳步逻辑
                self._add_skip_logic(
                    code_lines, step, steps, em_variable, action_num, i
                )

        code_lines.append("\tEND_CASE;")
        code_lines.append("END_IF;")
        code_lines.append("")

        return "\n".join(code_lines)

    def _add_motion_logic(
        self, code_lines: List[str], step: Dict, em_variable: str, em_name: str
    ):
        """添加运动逻辑"""
        action_desc = self.safe_get_string(step, "action_description")
        recovery_interlock_condition = self.safe_get_string(
            step, "recovery_interlock_condition"
        )
        recovery_interlock_action = self.safe_get_string(
            step, "recovery_interlock_action"
        )
        normal_conditions = self.parse_conditions(
            self.safe_get_string(step, "normal_interlock_condition")
        )

        if action_desc:
            code_lines.append("\t\t\t//运动")
            code_lines.append(f"\t\t\tIF {em_variable}.bCmdRun THEN")

            actions = self.parse_action_descriptions(action_desc)
            recovery_interlock_conditions = self.parse_recovery_conditions(
                recovery_interlock_condition
            )
            recovery_interlock_actions = self.parse_recovery_action_descriptions(
                recovery_interlock_action
            )

            # 动作和条件一一对应
            for i, action in enumerate(actions):
                # 获取对应的条件
                condition = "TRUE"
                if i < len(normal_conditions):
                    condition = normal_conditions[i]

                # 解析条件
                parsed_condition = self.parse_condition(condition, step)

                # 生成动作命令
                code_lines.append(f"\t\t\t\tIF {parsed_condition} THEN")
                self._add_single_action_command(
                    code_lines, action, em_name, "\t\t\t\t\t"
                )

                # 只在有实际恢复动作时才生成ELSIF分支
                if (
                    i < len(recovery_interlock_conditions)
                    and recovery_interlock_conditions[i]
                ):
                    for recovery_condition, recovery_action in zip(
                        recovery_interlock_conditions[i],
                        recovery_interlock_actions[i],
                    ):
                        # 检查恢复动作是否为空或只是空白字符
                        if recovery_action and recovery_action.strip():
                            parsed_recovery_condition = self.parse_condition(
                                recovery_condition, step
                            )
                            code_lines.append(
                                f"\t\t\t\tELSIF {parsed_recovery_condition} THEN"
                            )
                            self._add_single_action_command(
                                code_lines, recovery_action, em_name, "\t\t\t\t\t"
                            )

                code_lines.append("\t\t\t\tEND_IF;")

            code_lines.append("\t\t\tEND_IF;")

    def _add_single_action_command(
        self, code_lines: List[str], action: str, em_name: str, indent: str
    ):
        """添加单个动作命令"""
        # 首先尝试使用条件映射表查找动作
        step_data = {"em_variable": em_name, "op_sequence_under_em": "OP[1]"}
        mappings = self.build_condition_mappings()
        mapped_action = self.find_matching_condition(action, mappings, step_data)

        if mapped_action:
            # 如果在映射表中找到了对应的动作，直接使用
            code_lines.append(f"{indent}{mapped_action}:=TRUE;")
            return

        # 如果映射表中没有找到，使用原有的硬编码逻辑
        # 提取执行器名称和动作
        actuator_match = re.search(r"([A-Za-z]+\d+)(.+)", action)
        if actuator_match:
            actuator_part = actuator_match.group(1)  # 如 "SV1"
            action_part = actuator_match.group(2).strip()  # 如 "回零"

            # 转换执行器名称格式
            formatted_actuator = self.extract_actuator_name(actuator_part)

            # 根据动作类型生成对应的命令
            if "回零" in action_part:
                code_lines.append(
                    f"{indent}{em_name}.{formatted_actuator}.Ctrl.bHomeExe:=TRUE;"
                )
            elif "去缩位" in action_part or "去缸位" in action_part:
                code_lines.append(
                    f"{indent}{em_name}.{formatted_actuator}.bBw_A:= TRUE;"
                )
            elif "去伸位" in action_part or "去杆位" in action_part:
                code_lines.append(
                    f"{indent}{em_name}.{formatted_actuator}.bFw_A:= TRUE;"
                )
            elif "去位置1" in action_part:
                code_lines.append(
                    f"{indent}{em_name}.{formatted_actuator}.Ctrl.arbAToPos[1]:=TRUE;"
                )
            else:
                code_lines.append(f"{indent}// {action}")
        else:
            code_lines.append(f"{indent}// {action}")

    def _add_alarm_logic(self, code_lines: List[str], step: Dict, em_variable: str):
        """添加报警逻辑（支持分号分割的多个报警条件）"""
        alarm_condition = self.safe_get_string(step, "alarm_condition")
        if alarm_condition:
            # 按分号分割报警条件
            alarm_conditions = self.parse_conditions(alarm_condition)
            step_id = self.safe_get_int(step, "step_id", 0)

            if alarm_conditions:
                code_lines.append("\t\t\t//报警")

                for i, single_alarm in enumerate(alarm_conditions):
                    parsed_alarm = self.parse_condition(single_alarm, step)
                    if parsed_alarm != "TRUE" and parsed_alarm != "FALSE":
                        alarm_index = step_id + i  # 基础报警号 + 偏移量
                        code_lines.append(f"\t\t\tIF  {parsed_alarm} THEN")
                        code_lines.append(
                            f"\t\t\t\t {em_variable}.bAlm[{alarm_index}]:=TRUE;"
                        )
                        code_lines.append("\t\t\tEND_IF;")

    def _add_skip_logic(
        self,
        code_lines: List[str],
        step: Dict,
        all_steps: List[Dict],
        em_variable: str,
        action_num: int,
        step_index: int,
    ):
        """添加跳步逻辑"""
        skip_condition = self.safe_get_string(step, "skip_condition")

        if step_index == len(all_steps) - 1 or all_steps[step_index + 1] == "0":
            # 如果是最后一步，直接设置完成
            code_lines.append(f"\t\t\t{em_variable}.bDoneAction[{action_num}]:=TRUE;")
            code_lines.append(f"\t\t\t{em_variable}.iCmdStep:=0;")

        if skip_condition:
            parsed_skip = self.parse_condition(skip_condition, step)
            if parsed_skip != "FALSE":
                # 找到下一个步骤
                next_step_id = None
                if step_index + 1 < len(all_steps):
                    next_step_id = self.safe_get_int(
                        all_steps[step_index + 1], "step_id", 0
                    )

                code_lines.append("\t\t\t//跳步")
                code_lines.append(f"\t\t\tIF  {parsed_skip} THEN")

                if next_step_id is not None and next_step_id != 0:
                    code_lines.append(
                        f"\t\t\t\t {em_variable}.iCmdStep:={next_step_id};"
                    )
                else:
                    # 如果是最后一步，直接设置完成
                    code_lines.append(
                        f"\t\t\t\t {em_variable}.bDoneAction[{action_num}]:=TRUE;"
                    )
                    code_lines.append(f"\t\t\t\t {em_variable}.iCmdStep:=0;")

                code_lines.append("\t\t\tEND_IF;")

    def _is_last_non_zero_step(self, current_step: Dict, all_steps: List[Dict]) -> bool:
        """检查当前步骤是否是最后一个非0步骤"""
        current_step_id = self.safe_get_int(current_step, "step_id", 0)

        if current_step_id == 0:
            return False

        # 获取所有非0步骤并按step_id排序
        non_zero_steps = [
            step for step in all_steps if self.safe_get_int(step, "step_id", 0) > 0
        ]
        non_zero_steps.sort(key=lambda x: self.safe_get_int(x, "step_id", 0))

        if not non_zero_steps:
            return False

        # 检查当前步骤是否是最后一个
        last_step_id = self.safe_get_int(non_zero_steps[-1], "step_id", 0)
        return current_step_id == last_step_id

    def _has_completion_step(self, all_steps: List[Dict]) -> bool:
        """检查是否已经有完成步骤"""
        for step in all_steps:
            step_desc = self.safe_get_string(step, "step_description")
            if any(keyword in step_desc for keyword in ["完成", "等待"]):
                return True
        return False

    def generate_signal_reset_code(
        self, steps_data: List[Dict], max_actions: Optional[int] = None
    ) -> str:
        """生成信号自复位代码"""
        if not steps_data:
            return ""

        # 动态计算最大Action数量
        if max_actions is None:
            max_actions = self.calculate_max_actions(steps_data)

        first_step = steps_data[0]
        op_sequence_under_em = self.safe_get_string(
            first_step, "op_sequence_under_em", "OP[1]"
        )
        em_variable = self.safe_get_string(first_step, "em_variable", "io_stEmBtryIn")

        code_lines = []
        code_lines.append("//======================信号自复位=========================")
        code_lines.append("IF TRUE THEN")
        code_lines.append("\t//命令复位")

        for i in range(1, max_actions + 1):
            code_lines.append(
                f"\t {em_variable}.{op_sequence_under_em}.bCmdAction[{i}]:=FALSE;"
            )

        code_lines.append("\t//完成信号自复位")
        code_lines.append("END_IF;")
        code_lines.append("")

        return "\n".join(code_lines)

    def generate_single_em_op_code(
        self, em_description: str, steps_data: List[Dict]
    ) -> str:
        """生成单个EM的OP代码"""
        if not steps_data:
            return f"// 没有找到 {em_description} 的步骤数据"

        action_groups = self.group_steps_by_action(steps_data)
        all_code = []

        # 生成EM命令号转换
        em_conversion_code = self.generate_em_command_conversion(steps_data)
        all_code.append(em_conversion_code)

        # 生成EM状态机控制
        em_state_machine_code = self.generate_em_state_machine(steps_data)
        all_code.append(em_state_machine_code)

        # 生成各个Action流程
        for action_num in sorted(action_groups.keys()):
            steps = action_groups[action_num]
            process_code = self.generate_action_process_code(action_num, steps)
            all_code.append(process_code)

        # 生成信号自复位
        reset_code = self.generate_signal_reset_code(steps_data)
        all_code.append(reset_code)

        return "\n".join(all_code)

    def generate_all_op_code(self) -> str:
        """生成完整的OP代码（支持多个EM分块）"""
        # 清空未映射条件记录
        self.unmapped_conditions.clear()

        if not self.step_data:
            return "// 没有找到步骤数据"

        # 按EM描述分组
        em_groups = self.group_steps_by_em_description()
        all_code = []

        print(f"\n📊 发现 {len(em_groups)} 个EM模块:")
        for em_desc, steps in em_groups.items():
            print(f"  {em_desc}: {len(steps)} 个步骤")

        # 为每个EM生成代码
        for em_description, steps_data in em_groups.items():
            print(f"\n🔧 正在生成 {em_description} 的OP代码...")

            em_code = self.generate_single_em_op_code(em_description, steps_data)
            all_code.append(em_code)
            all_code.append("\n" + "=" * 100 + "\n")  # 分隔线

        # 检查条件映射完整性
        if self.unmapped_conditions:
            all_code.append("\n" + "/" * 80)
            all_code.append("// ⚠️  代码生成警告：存在未映射的条件")
            all_code.append("// 请检查上方的警告信息并完善条件映射表")
            all_code.append("/" * 80)

        return "\n".join(all_code)

    def check_condition_mappings(self) -> bool:
        """检查是否有未映射的条件"""
        if self.unmapped_conditions:
            print("\n" + "=" * 60)
            print("🚨 发现未映射的条件，请添加到条件映射表中:")
            print("=" * 60)
            for i, condition in enumerate(sorted(self.unmapped_conditions), 1):
                print(f"{i:2d}. '{condition}'")
            print("=" * 60)
            return False
        return True


if __name__ == "__main__":
    print("🚀 OP代码生成器 - 数据库版本")
    print("=" * 60)

    # 数据库配置
    db_config = {
        "host": "*************",
        "port": 3306,
        "user": "root",
        "password": "leadchina",
        "database": "IO_TEST",
        "charset": "utf8mb4",
    }

    # 表名
    op_table = "op_control_logic"
    actuator_table = "actuator_variable_map"

    def main_with_config():
        generator = OPCodeGenerator(db_config)

        try:
            if generator.connect_database():
                success = generator.load_all_data_from_db(op_table, actuator_table)

                if success:
                    print("\n🔧 开始生成OP代码...")
                    generated_code = generator.generate_all_op_code()

                    print("\n" + "=" * 80)
                    print("📄 生成的OP代码:")
                    print("=" * 80)
                    print(generated_code)

                    with open(
                        "tests/generated_op_code.txt", "w", encoding="utf-8"
                    ) as f:
                        f.write(generated_code)
                    print("\n💾 代码已保存到 generated_op_code.txt")

                    # 检查条件映射
                    generator.check_condition_mappings()

                else:
                    print("❌ 数据加载失败，请检查表名和数据结构")

        except Exception as e:
            print(f"❌ 程序执行出错: {e}")
        finally:
            generator.disconnect_database()

        return generator

    try:
        generator = main_with_config()
        print("\n✅ 程序执行完成!")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

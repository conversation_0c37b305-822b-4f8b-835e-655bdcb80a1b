stages:
  - build-web
  - build
  - deploy

workflow:
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+(-.*)?$/'  # 仅在存在 Tag 时触发

build_web:
  stage: build-web
  before_script:
    - echo "Setting up Node.js environment..."
    - npm config set registry=https://registry.npmmirror.com
  script:
    - echo "Building frontend..."
    - cd frontend
    - npm install
    - npm run build
    - cd ..
    - echo "Frontend build completed"
    - ls -la frontend/dist/
  artifacts:
    paths:
      - frontend/dist
    expire_in: 1 hour
  tags:
    - node-runner-20
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+(-.*)?$/'  # 仅在存在 Tag 时触发


# Build Docker Images job
build_docker_images:
  stage: build
  dependencies:
    - build_web
  variables:
    DOCKER_HOST:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - echo "Preparing Docker build environment..."
    - docker info
  script:
    - echo "Building Docker images for PLC Auto Coding System..."
    - ls -la
    - ls -la frontend/dist/
    - docker buildx create --name build-images --driver docker-container --config buildkitd.toml --use || docker buildx use build-images
    - MAJOR_TAG=$(echo $CI_COMMIT_TAG | awk -F. '{print $1}')
    - MINOR_TAG=$(echo $CI_COMMIT_TAG | awk -F. '{print $1"."$2}')
    - PATCH_TAG=$CI_COMMIT_TAG
    - echo "Building frontend image..."
    - >
      docker buildx build --platform linux/amd64
      -f Dockerfile.frontend
      -t *************:5000/plc-auto-coding-frontend:latest
      -t *************:5000/plc-auto-coding-frontend:$MAJOR_TAG
      -t *************:5000/plc-auto-coding-frontend:$MINOR_TAG
      -t *************:5000/plc-auto-coding-frontend:$PATCH_TAG
      --push .
    - echo "Building backend image..."
    - >
      docker buildx build --platform linux/amd64
      -f backend/Dockerfile
      -t *************:5000/plc-auto-coding-backend:latest
      -t *************:5000/plc-auto-coding-backend:$MAJOR_TAG
      -t *************:5000/plc-auto-coding-backend:$MINOR_TAG
      -t *************:5000/plc-auto-coding-backend:$PATCH_TAG
      --push .
  after_script:
    - docker buildx rm build-images || true
  tags:
    - docker-runner
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+(-.*)?$/'  # 仅在存在 Tag 时触发


deploy_to_production:
  stage: deploy
  dependencies:
    - build_docker_images
  before_script:
    - apt-get update -qq && apt-get install -y -qq sshpass
  script:
    - echo "Deploying PLC Auto Coding System to production..."
    - export SSHPASS=LEADchina123@@
    - PATCH_TAG=$CI_COMMIT_TAG
    - echo "Pulling latest images and restarting services..."
    - >
      sshpass -e ssh -o StrictHostKeyChecking=no root@************* "
      cd plc-auto-coding/ &&
      docker pull *************:5000/plc-auto-coding-frontend:$PATCH_TAG &&
      docker pull *************:5000/plc-auto-coding-backend:$PATCH_TAG &&
      docker compose down &&
      export PLC_FRONTEND_TAG=$PATCH_TAG &&
      export PLC_BACKEND_TAG=$PATCH_TAG &&
      docker compose up -d &&
      docker image prune -f
      "
    - echo "Deployment completed successfully"
  tags:
    - ubuntu-runner-22.04
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+(-.*)?$/'  # 仅在存在 Tag 时触发

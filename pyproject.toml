[project]
name = "plc-auto-coding"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "pandas>=2.2.3",
    "pymysql>=1.1.1",
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.23",
    "pydantic>=2.5.0",
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-dotenv>=1.0.0",
    "openpyxl>=3.1.2",
    "pydantic-settings>=2.0.0",
    "requests>=2.32.4",
    "fastmcp>=2.10.5",
    "mcp>=1.11.0",
    "sse-starlette>=2.4.1",
]

[dependency-groups]
dev = [
    "mypy>=1.16.0",
    "pre-commit>=4.2.0",
    "types-pymysql>=1.1.0.20250516",
]

[tool.mypy]
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "pymysql.*"
ignore_missing_imports = true

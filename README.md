# PLC自动编码系统 - 数据库表维护前后端服务

基于现有的TP和OP代码生成功能，提供完整的Web界面来维护数据库表并支持在线代码生成。

## 🚀 功能特性

### 核心功能
- **数据库表管理**: 完整的CRUD操作支持三张核心表
  - TP控制逻辑表 (`tp_control_logic`)
  - OP控制逻辑表 (`op_control_logic`)
  - 执行器变量映射表 (`actuator_variable_map`)

- **代码生成**: 集成现有的代码生成逻辑
  - TP代码生成（工艺流程）
  - OP代码生成（操作流程）
  - 在线代码预览和语法高亮
  - 代码文件下载

- **条件映射验证**: 智能验证系统
  - 实时检查未映射条件
  - 提供详细的验证报告
  - 映射建议和修复指导

### 界面特性
- **现代化UI**: 基于Ant Design的美观界面
- **响应式设计**: 适配不同屏幕尺寸
- **智能搜索**: 多条件筛选和分页查询
- **批量操作**: 支持批量编辑和删除
- **实时预览**: Monaco编辑器提供代码高亮

## 📦 快速开始

### 方式一：Docker一键部署（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd plc-auto-coding

# 一键启动
./scripts/start.sh
```

启动后访问：
- 🌐 **Web界面**: http://localhost
- 📚 **API文档**: http://localhost/docs
- 🔧 **API调试**: http://localhost/redoc

### 方式二：开发环境部署

#### 1. 后端服务

```bash
# 安装依赖
pip install -e .

# 配置环境变量
cp .env.example .env
# 编辑.env文件配置数据库连接

# 启动后端服务
cd backend
python run.py
```

#### 2. 前端服务

```bash
# 安装依赖
cd frontend
npm install

# 启动开发服务器
npm run dev
```

## Integrate with your tools

- [ ] [Set up project integrations](http://*************:8929/Expert_System/Algorithms/plc-auto-coding/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Set auto-merge](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing (SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thanks to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README

Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.

version: '3.8'

services:
  # 后端API服务
  backend:
    image: *************:5000/plc-auto-coding-backend:${PLC_BACKEND_TAG:-latest}
    environment:
      - DATABASE_HOST=*************
      - DATABASE_PORT=3306
      - DATABASE_USER=root
      - DATABASE_PASSWORD=leadchina
      - DATABASE_NAME=IO_TEST
      - DEBUG=false
    restart: unless-stopped

  # Nginx反向代理和前端静态文件服务
  nginx:
    image: *************:5000/plc-auto-coding-frontend:${PLC_FRONTEND_TAG:-latest}
    ports:
      - "30091:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
    restart: unless-stopped

  mcpo-server:
    depends_on:
      - backend
    image: ghcr.io/open-webui/mcpo:main
    container_name: mcpo-server-instance # (可选) 为容器指定一个易于识别的名称
    ports:
      - "30092:8000" # 将主机的8000端口映射到容器的8000端口
    volumes:
      - ./mcpo_config.json:/app/config.json
    command:
      - "--api-key"
      - "${MCPO_API_KEY}"
      - "--config"
      - "/app/config.json"
    restart: unless-stopped # (可选) 推荐设置重启策略，例如除非手动停止否则总是重启
    env_file:
      - .env

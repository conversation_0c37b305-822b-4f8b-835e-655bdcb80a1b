#!/bin/bash

# PLC自动编码系统启动脚本

echo "🚀 启动PLC自动编码系统"
echo "=========================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  警告: 端口 $port ($service) 已被占用"
        read -p "是否继续启动? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

echo "🔍 检查端口占用情况..."
check_port 80 "Nginx"
check_port 3000 "前端"
check_port 8000 "后端API"
check_port 3306 "MySQL"

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p uploads
mkdir -p generated_codes
mkdir -p logs

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 等待数据库初始化
echo "⏳ 等待数据库初始化..."
sleep 5

# 检查服务健康状态
echo "🏥 检查服务健康状态..."

# 检查后端API
if curl -f http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ 后端API服务正常"
else
    echo "❌ 后端API服务异常"
fi

# 检查前端服务
if curl -f http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
fi

# 检查Nginx
if curl -f http://localhost >/dev/null 2>&1; then
    echo "✅ Nginx服务正常"
else
    echo "❌ Nginx服务异常"
fi

echo ""
echo "🎉 系统启动完成!"
echo "=========================="
echo "📱 前端界面: http://localhost"
echo "🔧 API文档: http://localhost/docs"
echo "📊 API调试: http://localhost/redoc"
echo "🏥 健康检查: http://localhost/health"
echo ""
echo "📋 常用命令:"
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  查看状态: docker-compose ps"
echo ""
echo "💡 如果遇到问题，请检查日志文件或联系技术支持"

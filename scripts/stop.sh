#!/bin/bash

# PLC自动编码系统停止脚本

echo "🛑 停止PLC自动编码系统"
echo "=========================="

# 检查Docker Compose是否运行
if ! docker-compose ps | grep -q "Up"; then
    echo "ℹ️  系统未运行或已停止"
    exit 0
fi

# 停止所有服务
echo "🔄 停止所有服务..."
docker-compose down

# 可选：清理数据卷（谨慎使用）
read -p "是否清理数据卷? 这将删除所有数据库数据 (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  清理数据卷..."
    docker-compose down -v
    echo "⚠️  数据库数据已清理"
fi

# 可选：清理镜像
read -p "是否清理构建的镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  清理镜像..."
    docker-compose down --rmi all
    echo "🧹 镜像已清理"
fi

echo ""
echo "✅ 系统已停止"
echo "=========================="
echo "💡 如需重新启动，请运行: ./scripts/start.sh"

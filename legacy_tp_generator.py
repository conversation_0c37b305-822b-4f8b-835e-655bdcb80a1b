import pymysql
from typing import Dict, List, Optional, Set
import re


class PLCCodeGenerator:
    def __init__(self, db_config: Optional[Dict] = None):
        self.step_data: List[Dict] = []
        self.command_data: List[Dict] = []
        self.db_config = db_config
        self.connection = None
        self.unmapped_conditions: Set = set()  # 记录未映射的条件

    @staticmethod
    def safe_get_string(data_dict: Dict, key: str, default: str = "") -> str:
        """安全获取字符串值，处理None和空值"""
        value = data_dict.get(key)
        if value is None:
            return default
        if isinstance(value, str):
            return value.strip()
        return str(value).strip()

    @staticmethod
    def safe_get_int(data_dict: Dict, key: str, default: int = 0) -> int:
        """安全获取整数值，处理None和转换错误"""
        value = data_dict.get(key)
        if value is None:
            return default
        try:
            return int(value)
        except (ValueError, TypeError):
            return default

    def connect_database(self, db_config: Optional[Dict] = None):
        """连接MySQL数据库"""
        if db_config:
            self.db_config = db_config

        if not self.db_config:
            raise ValueError("数据库配置信息不能为空")

        try:
            # 使用pymysql连接（推荐）
            self.connection = pymysql.connect(
                host=self.db_config.get("host", "localhost"),
                port=self.db_config.get("port", 3306),
                user=self.db_config.get("user"),
                password=self.db_config.get("password"),
                database=self.db_config.get("database"),
                charset=self.db_config.get("charset", "utf8mb4"),
                autocommit=True,
            )
            print("数据库连接成功!")
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False

    def disconnect_database(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            print("数据库连接已关闭")

    def load_step_data_from_db(self, table_name: str = "step_sequence_table"):
        """从数据库加载步骤数据"""
        if not self.connection:
            raise ConnectionError("请先连接数据库")

        try:
            query = f"""
            SELECT id, step_id, step_description, motion_condition, motion_action,
                   skip_condition, skip_step, tp_sequence, em_description_cn,
                   em_variable, op_sequence_under_em
            FROM {table_name}
            ORDER BY id
            """

            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute(query)
            self.step_data = cursor.fetchall()
            cursor.close()

            print(f"成功加载 {len(self.step_data)} 条步骤数据")
            return self.step_data

        except Exception as e:
            print(f"加载步骤数据失败: {e}")
            return []

    def load_command_data_from_db(self, table_name: str = "command_mapping_table"):
        """从数据库加载命令数据"""
        if not self.connection:
            raise ConnectionError("请先连接数据库")

        try:
            query = f"""
            SELECT id, parent_struct, actuator, command_variable, command_description_cn
            FROM {table_name}
            ORDER BY id
            """

            cursor = self.connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute(query)
            self.command_data = cursor.fetchall()
            cursor.close()

            print(f"成功加载 {len(self.command_data)} 条命令数据")
            return self.command_data

        except Exception as e:
            print(f"加载命令数据失败: {e}")
            return []

    def load_all_data_from_db(
        self,
        step_table: str = "step_sequence_table",
        command_table: str = "command_mapping_table",
    ):
        """从数据库加载所有数据"""
        self.load_step_data_from_db(step_table)
        self.load_command_data_from_db(command_table)
        return len(self.step_data) > 0 and len(self.command_data) > 0

    def parse_step_data_from_text(self, step_text: str):
        """从文本解析步骤数据"""
        lines = step_text.strip().split("\n")
        for line in lines:
            if line.strip():
                parts = line.split()
                if len(parts) >= 11:  # 新格式需要至少11个字段
                    step_record = {
                        "id": int(parts[0]),
                        "step_id": int(parts[1]),
                        "step_description": parts[2],
                        "motion_condition": parts[3] if parts[3] != "TRUE" else "TRUE",
                        "motion_action": parts[4],  # 新增：运动动作
                        "skip_condition": parts[5]
                        if parts[5] != "FALSE"
                        else "FALSE",  # 新增：跳步条件
                        "skip_step": parts[6],  # 新增：跳步目标
                        "tp_sequence": parts[7],
                        "em_description_cn": parts[8],
                        "em_variable": parts[9],
                        "op_sequence_under_em": parts[10],
                    }
                    self.step_data.append(step_record)

    def parse_command_data_from_text(self, command_text: str):
        """从文本解析命令数据"""
        lines = command_text.strip().split("\n")
        for line in lines:
            if line.strip():
                parts = line.split()
                if len(parts) >= 4:
                    command_record = {
                        "id": int(parts[0]),
                        "parent_struct": parts[1],
                        "actuator": parts[2],
                        "command_variable": parts[3],
                        "command_description_cn": " ".join(parts[4:])
                        if len(parts) > 4
                        else "",
                    }
                    self.command_data.append(command_record)

    def group_steps_by_tp(self) -> Dict[str, List[Dict]]:
        """按TP序列分组步骤"""
        tp_groups: Dict[str, List] = {}
        for step in self.step_data:
            tp_seq = step["tp_sequence"]
            if tp_seq not in tp_groups:
                tp_groups[tp_seq] = []
            tp_groups[tp_seq].append(step)

        # 按step_id排序
        for tp_seq in tp_groups:
            tp_groups[tp_seq].sort(key=lambda x: x["step_id"])

        return tp_groups

    def extract_tp_number(self, tp_sequence: str) -> str:
        """提取TP编号"""
        match = re.search(r"TP\[(\d+)\]", tp_sequence)
        return match.group(1) if match else "1"

    def build_condition_mappings(self) -> Dict[str, Dict[str, str]]:
        """从数据库的命令映射表构建条件映射字典（参考OP代码生成器的方式）"""
        condition_mappings = {}

        for cmd in self.command_data:
            description = self.safe_get_string(cmd, "command_description_cn")
            if description:
                condition_mappings[description] = cmd

        return condition_mappings

    def extract_actuator_name(self, text: str) -> str:
        """从文本中提取执行器名称（如SV1、YV2等）"""
        # 匹配字母+数字的组合
        match = re.search(r"([A-Za-z]+)(\d+)", text)
        if match:
            letters = match.group(1)
            numbers = match.group(2)
            return f"{letters}[{numbers}]"
        return text

    def find_matching_condition(
        self, condition_text: str, mappings: Dict[str, Dict[str, str]], step: Dict
    ) -> Optional[str]:
        """在映射表中查找匹配的条件（参考OP代码生成器的逻辑）"""
        # 1. 直接匹配
        if condition_text in mappings:
            actuator_type = self.safe_get_string(mappings[condition_text], "actuator")

            if actuator_type == "Y":
                # Y表示动态拼接机构，需要处理执行器名称替换
                return None  # 这种情况需要特殊处理，暂时返回None
            elif actuator_type == "N":
                # N表示没有执行器，直接拼接parent_struct和command_variable
                parent_struct = self.safe_get_string(
                    mappings[condition_text], "parent_struct"
                )
                command_var = self.safe_get_string(
                    mappings[condition_text], "command_variable"
                )
                if parent_struct:
                    return f"{parent_struct}.{command_var}"
                else:
                    return command_var
            elif actuator_type == "OP":
                # OP表示拼接OP序列
                parent_struct = self.safe_get_string(
                    mappings[condition_text], "parent_struct"
                )
                command_var = self.safe_get_string(
                    mappings[condition_text], "command_variable"
                )
                op_sequence = self.safe_get_string(
                    step, "op_sequence_under_em", "OP[1]"
                )
                if parent_struct:
                    return f"{parent_struct}.{op_sequence}.{command_var}"
                else:
                    return f"{op_sequence}.{command_var}"

        # 2. 提取执行器和条件部分进行匹配（处理动态拼接的情况）
        actuator_match = re.search(r"([A-Za-z]+\d+)(.+)", condition_text)
        if actuator_match:
            actuator_part = actuator_match.group(1)  # 如 "SV1"
            condition_part = actuator_match.group(2).strip()  # 如 "在零点"

            # 在映射表中查找条件部分
            for desc, mapping_data in mappings.items():
                # 简单关键词匹配
                if condition_part in desc:
                    actuator_type = self.safe_get_string(mapping_data, "actuator")
                    if actuator_type == "Y":
                        # 替换执行器名称
                        formatted_actuator = self.extract_actuator_name(actuator_part)
                        return self._replace_actuator_in_path(
                            mapping_data, formatted_actuator
                        )
                    else:
                        return None

        # 如果找不到匹配，返回None
        return None

    def _replace_actuator_in_path(
        self, var_path: Dict[str, str], formatted_actuator: str
    ) -> str:
        """在变量路径中替换执行器名称"""
        parent_struct = self.safe_get_string(var_path, "parent_struct")
        command_var = self.safe_get_string(var_path, "command_variable")

        if parent_struct:
            return f"{parent_struct}.{formatted_actuator}.{command_var}"
        else:
            return f"{formatted_actuator}.{command_var}"

    def generate_condition_mapping(
        self, condition_text: str, step: Optional[Dict] = None
    ) -> str:
        """生成条件映射（参考OP代码生成器的解析方式）"""
        if not condition_text or condition_text.strip() == "":
            return "FALSE"

        condition_text = condition_text.strip()

        if condition_text == "TRUE":
            return "TRUE"
        elif condition_text == "FALSE":
            return "FALSE"
        else:
            # 检查是否是PLC变量格式（字母、下划线、方括号、数字的组合）
            if self._is_plc_variable(condition_text):
                return condition_text

            # 添加一些基本的条件映射
            basic_mappings = self._get_basic_condition_mappings(step)

            # 检查基本映射
            if condition_text in basic_mappings:
                return basic_mappings[condition_text]

            # 处理复合条件（包含AND、OR、NOT或括号的条件）
            if (
                "AND" in condition_text
                or "OR" in condition_text
                or "NOT" in condition_text
                or "(" in condition_text
                or ")" in condition_text
                or "_AND_" in condition_text
                or "_OR_" in condition_text
                or condition_text.startswith("NOT_")
            ):
                return self._parse_complex_condition_new(condition_text, step)

            # 从数据库命令表构建条件映射
            condition_mappings = self.build_condition_mappings()

            # 使用高级解析方法处理条件
            result = self._advanced_parse_condition(
                condition_text, condition_mappings, step or {}
            )

            return result

    def _is_plc_variable(self, text: str) -> bool:
        """检查是否是完整的PLC变量格式（包含点号的完整路径）"""
        import re

        # 完整PLC变量模式：必须包含点号，表示完整路径
        pattern = r"^[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z0-9_.\[\]]+$"
        return bool(re.match(pattern, text))

    def _get_basic_condition_mappings(
        self, step: Optional[Dict] = None
    ) -> Dict[str, str]:
        """从数据库获取基本条件映射"""
        if not step:
            return {}

        em_variable = step.get("em_variable", "io_stEmBtryIn")
        op_sequence = step.get("op_sequence_under_em", "OP[1]")

        # 从数据库构建条件映射
        condition_mappings = self.build_condition_mappings()
        basic_mappings = {}

        for description, mapping_data in condition_mappings.items():
            parent_struct = self.safe_get_string(mapping_data, "parent_struct")
            actuator_type = self.safe_get_string(mapping_data, "actuator")
            command_var = self.safe_get_string(mapping_data, "command_variable")

            if actuator_type == "Y":
                # Y代表动态获取em进行拼接
                if parent_struct and parent_struct != "Y":
                    basic_mappings[description] = (
                        f"{em_variable}.{parent_struct}.{command_var}"
                    )
                else:
                    basic_mappings[description] = f"{em_variable}.{command_var}"
            elif actuator_type == "N":
                # N代表不进行拼接，直接使用command_variable
                basic_mappings[description] = command_var
            elif actuator_type == "OP":
                # OP代表动态获取OP进行拼接
                if parent_struct and parent_struct != "OP":
                    basic_mappings[description] = (
                        f"{em_variable}.{op_sequence}.{command_var}"
                    )
                else:
                    basic_mappings[description] = f"{op_sequence}.{command_var}"

        # 处理bDoneAction[N]的动态拼接，支持1-99
        for i in range(1, 100):  # 支持bDoneAction[1]到bDoneAction[99]
            basic_mappings[f"bDoneAction[{i}]"] = (
                f"{em_variable}.{op_sequence}.bDoneAction[{i}]"
            )

        # 同样处理其他可能的动作数组
        for i in range(1, 100):
            basic_mappings[f"bCmdAction[{i}]"] = (
                f"{em_variable}.{op_sequence}.bCmdAction[{i}]"
            )
            basic_mappings[f"bConAction[{i}]"] = (
                f"{em_variable}.{op_sequence}.bConAction[{i}]"
            )
            basic_mappings[f"bSkipAction[{i}]"] = (
                f"{em_variable}.{op_sequence}.bSkipAction[{i}]"
            )
            basic_mappings[f"bAlm[{i}]"] = f"{em_variable}.{op_sequence}.bAlm[{i}]"

        return basic_mappings

    def _parse_complex_condition_new(
        self, condition_text: str, step: Optional[Dict] = None
    ) -> str:
        """解析新格式的复合条件（用下划线分隔）"""
        basic_mappings = self._get_basic_condition_mappings(step)

        # 直接使用智能解析，不管是否有括号
        return self._parse_complex_condition_with_parentheses(
            condition_text, basic_mappings, step
        )

    def _parse_complex_condition_with_parentheses(
        self,
        condition_text: str,
        basic_mappings: Dict[str, str],
        step: Optional[Dict] = None,
    ) -> str:
        """处理包含括号的复杂条件"""
        # 对于包含括号的条件，我们需要更仔细地处理
        # 例如：bDoneAction[1] AND (不带托盘空运行 OR 带托盘空运行)

        import re

        result = condition_text

        # 第一步：处理bDoneAction[N]和bCmdAction[N]等模式
        action_pattern = (
            r"\b(bDoneAction|bCmdAction|bConAction|bSkipAction|bAlm)\[(\d+)\]"
        )

        def replace_action(match):
            action_type = match.group(1)
            number = match.group(2)
            original = f"{action_type}[{number}]"
            if original in basic_mappings:
                return basic_mappings[original]
            return original

        result = re.sub(action_pattern, replace_action, result)

        # 第二步：按优先级替换其他映射（长的先替换，避免部分匹配）
        sorted_mappings = sorted(
            basic_mappings.items(), key=lambda x: len(x[0]), reverse=True
        )

        for description, mapping in sorted_mappings:
            # 跳过已经处理的动作模式
            if re.match(r".*\[\d+\]$", description):
                continue

            # 使用完整匹配
            if description in result:
                result = result.replace(description, mapping)

        # 第三步：检查是否还有未映射的内容
        # 重新检查结果中是否还有未映射的条件

        # 查找所有已知的PLC变量（包含方括号的完整变量）
        plc_var_pattern = (
            r"\b[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z0-9_]+)*(\[[a-zA-Z0-9_]+\])*"
        )

        # 创建一个临时结果用于检查未映射条件
        temp_result = result

        # 移除所有已知的PLC变量
        temp_result = re.sub(plc_var_pattern, " ", temp_result)

        # 移除逻辑运算符和括号
        temp_result = re.sub(r"\b(AND|OR|NOT)\b", " ", temp_result)
        temp_result = re.sub(r"[()[\]]", " ", temp_result)  # 移除所有括号

        # 剩下的非空白内容就是未映射的条件
        remaining_tokens = [
            token.strip() for token in temp_result.split() if token.strip()
        ]

        for token in remaining_tokens:
            # 过滤掉单个字符和数字
            if token and len(token) > 1 and not token.isdigit():
                self.unmapped_conditions.add(token)

        return result

    def _advanced_parse_condition(
        self, condition_text: str, mappings: Dict[str, Dict[str, str]], step: Dict
    ) -> str:
        """高级条件解析 - 处理更复杂的逻辑表达式（参考OP代码生成器）"""
        # 处理 NOT（优先级最高）
        if condition_text.startswith("NOT "):
            base_condition = condition_text[4:].strip()
            mapped_base = self._advanced_parse_condition(base_condition, mappings, step)
            return f"NOT {mapped_base}"

        # 处理 OR（优先级最低）
        if " OR " in condition_text:
            or_parts = condition_text.split(" OR ")
            mapped_parts = []
            for part in or_parts:
                part = part.strip()
                mapped_part = self._advanced_parse_condition(part, mappings, step)
                mapped_parts.append(mapped_part)
            return " OR ".join(mapped_parts)

        # 处理 AND
        if " AND " in condition_text:
            and_parts = condition_text.split(" AND ")
            mapped_parts = []
            for part in and_parts:
                part = part.strip()
                mapped_part = self._advanced_parse_condition(part, mappings, step)
                mapped_parts.append(mapped_part)
            return " AND ".join(mapped_parts)

        # 处理单一条件
        result = self.find_matching_condition(condition_text, mappings, step)

        if result:
            return result

        # 如果找不到匹配，记录未映射条件
        self.unmapped_conditions.add(condition_text)
        return f"/* 未映射: {condition_text} */"

    def check_condition_mappings(self) -> bool:
        """检查是否有未映射的条件"""
        if self.unmapped_conditions:
            print("\n" + "=" * 60)
            print("🚨 发现未映射的条件，请添加到条件映射表中:")
            print("=" * 60)
            for i, condition in enumerate(sorted(self.unmapped_conditions), 1):
                print(f"{i:2d}. '{condition}'")
            print("=" * 60)
            print("⚠️  建议：在 generate_condition_mapping 方法中添加这些条件的映射")
            print("⚠️  警告：未映射的条件将生成注释代码，可能导致PLC程序无法正常运行")
            return False
        return True

    def get_condition_mapping_template(self) -> str:
        """生成条件映射模板代码"""
        if not self.unmapped_conditions:
            return "# 所有条件都已正确映射"

        template = (
            "# 发现未映射的条件，需要在数据库的command_mapping_table中添加对应记录:\n"
        )
        template += "# INSERT INTO command_mapping_table (parent_struct, actuator, command_variable, command_description_cn) VALUES\n"

        for condition in sorted(self.unmapped_conditions):
            template += f"# ('your_parent_struct', 'your_actuator', 'your_command_variable', '{condition}'),\n"

        template += "\n# 或者检查数据库中是否已存在对应记录，可能是描述文字不完全匹配\n"
        return template

    def print_condition_mappings_info(self):
        """打印当前的条件映射信息"""
        mappings = self.build_condition_mappings()
        print(f"\n📋 从数据库加载了 {len(mappings)} 个条件映射:")
        print("=" * 60)
        for description, mapping_data in sorted(mappings.items()):
            actuator_type = self.safe_get_string(mapping_data, "actuator")
            parent_struct = self.safe_get_string(mapping_data, "parent_struct")
            command_var = self.safe_get_string(mapping_data, "command_variable")
            print(f"  '{description}' -> {parent_struct}.{actuator_type}.{command_var}")
        print("=" * 60)

    def generate_tp_process_code(self, tp_sequence: str, steps: List[Dict]) -> str:
        """生成单个TP流程代码"""
        tp_num = self.extract_tp_number(tp_sequence)
        em_variable = steps[0]["em_variable"] if steps else "io_stEmBtryIn"
        op_sequence = steps[0]["op_sequence_under_em"] if steps else "OP[1]"
        em_description = steps[0]["em_description_cn"] if steps else ""

        code_lines = []
        code_lines.append(
            f"//====================================TP{tp_num}{em_description}自动流程===================================="
        )
        code_lines.append("IF io_stUm.CmdSta.iCmdNo=2 THEN")
        code_lines.append(f"\tCASE io_stUm.TP[{tp_num}].iCmdStep OF")

        steps = sorted(steps, key=lambda x: int(x["step_id"]))

        for i, step in enumerate(steps):
            step_id = step["step_id"]
            step_desc = step["step_description"]

            if step_id == 0:
                # 等待启动信号的特殊处理
                code_lines.append(
                    f"\t\t{step_id}:\tio_stUm.TP[{tp_num}].strStepInfo:='{step_desc}';"
                )
                code_lines.append("\t\t\tIF io_stUm.CmdSta.bCmdRun THEN\t")

                # 找到下一个步骤
                next_step = self._find_next_step(steps, i)
                if next_step:
                    code_lines.append(
                        f"\t\t\t\tio_stUm.TP[{tp_num}].iCmdStep:={next_step};"
                    )
                code_lines.append("\t\t\tEND_IF;")

            elif step_id == 500:
                # 结束步骤的特殊处理
                code_lines.append(
                    f"\t\t{step_id}:io_stUm.TP[{tp_num}].strStepInfo:='{step_desc}';"
                )
                code_lines.append(f"\t\t\tio_stUm.TP[{tp_num}].bCmdDone:=TRUE;")
                code_lines.append(f"\t\t\tio_stUm.TP[{tp_num}].iCmdStep:=0;")

            else:
                # 普通步骤处理
                code_lines.append(f"\t\t{step_id}://{step_desc}")
                code_lines.append(
                    f"\t\t\tio_stUm.TP[{tp_num}].strStepInfo:={em_variable}.{op_sequence}.strStepInfo;"
                )

                # 检查是否有运动逻辑（motion_condition和motion_action都不为空）
                motion_condition = step.get("motion_condition", "")
                motion_action = step.get("motion_action", "")

                if (
                    motion_condition
                    and motion_condition.strip()
                    and motion_action
                    and motion_action.strip()
                ):
                    # 添加运动条件和动作
                    self._add_motion_logic_new(
                        code_lines, step, em_variable, op_sequence
                    )

                # 添加跳步逻辑
                self._add_skip_logic_new(code_lines, step, tp_num)

            code_lines.append("")

        code_lines.append("\tEND_CASE;")
        code_lines.append("END_IF;")
        code_lines.append("")

        return "\n".join(code_lines)

    def _find_next_step(self, steps: List[Dict], current_index: int) -> str:
        """查找下一个步骤ID"""
        if current_index + 1 < len(steps):
            return steps[current_index + 1]["step_id"]
        return "500"  # 默认结束步骤

    def _add_motion_logic_new(
        self, code_lines: List[str], step: Dict, em_variable: str, op_sequence: str
    ):
        """添加新的运动逻辑"""
        motion_condition = step.get("motion_condition", "TRUE")
        motion_action = step.get("motion_action", "")

        code_lines.append("\t\t    IF io_stUm.CmdSta.bCmdRun THEN")

        # 解析运动条件
        parsed_condition = self.generate_condition_mapping(motion_condition, step)
        code_lines.append(f"\t\t\t\tIF  {parsed_condition} THEN")

        # 添加运动动作
        if motion_action and motion_action.strip():
            code_lines.append(
                f"\t\t\t\t\t {em_variable}.{op_sequence}.{motion_action}:=TRUE;"
            )

        code_lines.append("\t\t\t\tEND_IF;")
        code_lines.append("\t\t\tEND_IF;")
        code_lines.append("")

    def _add_skip_logic_new(self, code_lines: List[str], step: Dict, tp_num: str):
        """添加新的跳步逻辑"""
        skip_condition = step.get("skip_condition", "")
        skip_step = step.get("skip_step", "")

        if (
            not skip_condition
            or skip_condition.strip() == ""
            or skip_condition == "FALSE"
        ):
            return

        # 解析跳步条件和目标步骤（支持分号分隔的多条件多目标）
        conditions = [c.strip() for c in skip_condition.split(";") if c.strip()]
        steps_list = [s.strip() for s in skip_step.split(";") if s.strip()]

        if not conditions:
            return

        # 生成IF-ELSIF结构
        for i, condition in enumerate(conditions):
            parsed_condition = self.generate_condition_mapping(condition, step)
            target_step = (
                steps_list[i] if i < len(steps_list) else steps_list[-1]
            )  # 如果目标步骤不够，使用最后一个

            if i == 0:
                code_lines.append(f"\t\t\tIF {parsed_condition} THEN")
            else:
                code_lines.append(f"\t\t\tELSIF {parsed_condition} THEN")

            code_lines.append(f"\t\t\t\tio_stUm.TP[{tp_num}].iCmdStep:={target_step};")

        code_lines.append("\t\t\tEND_IF;")

    def generate_all_code(self) -> str:
        """生成所有PLC代码"""
        # 清空未映射条件记录
        self.unmapped_conditions.clear()

        tp_groups = self.group_steps_by_tp()
        all_code = []

        # 生成每个TP流程的代码
        for tp_sequence, steps in tp_groups.items():
            process_code = self.generate_tp_process_code(tp_sequence, steps)
            all_code.append(process_code)
            all_code.append("")

        # 检查条件映射完整性
        mapping_check = self.check_condition_mappings()

        # 如果有未映射条件，在代码末尾添加提示
        if not mapping_check:
            all_code.append("\n" + "/" * 80)
            all_code.append("// ⚠️  代码生成警告：存在未映射的条件")
            all_code.append("// 请检查上方的警告信息并完善条件映射表")
            all_code.append("/" * 80)

        return "\n".join(all_code)


if __name__ == "__main__":
    print("🚀 PLC代码生成器 - 数据库版本")
    print("=" * 60)

    # 获取数据库配置
    db_config = {
        "host": "*************",  # 数据库服务器地址
        "port": 3306,  # 端口号
        "user": "root",  # 用户名
        "password": "leadchina",  # 密码
        "database": "IO_TEST",  # 数据库名
        "charset": "utf8mb4",  # 字符集
    }

    # 获取表名
    tp_step_table = "tp_control_logic"
    op_step_table = "op_control_logic"
    command_table = "actuator_variable_map"

    # 更新数据库配置到主函数
    def main_with_config():
        generator = PLCCodeGenerator(db_config)

        try:
            if generator.connect_database():
                success = generator.load_all_data_from_db(tp_step_table, command_table)

                if success:
                    generator.print_condition_mappings_info()

                    tp_groups = generator.group_steps_by_tp()
                    print(f"\n📊 发现 {len(tp_groups)} 个TP流程:")
                    for tp_seq, steps in tp_groups.items():
                        em_desc = steps[0]["em_description_cn"] if steps else ""
                        print(f"  {tp_seq}: {em_desc} ({len(steps)} 个步骤)")

                    print("\n🔧 开始生成PLC代码...")
                    generated_code = generator.generate_all_code()

                    print("\n" + "=" * 80)
                    print("📄 生成的PLC代码:")
                    print("=" * 80)
                    print(generated_code)

                    if generator.unmapped_conditions:
                        print("\n" + "🔧 数据库条件映射补充建议:")
                        print("=" * 60)
                        print(generator.get_condition_mapping_template())

                    with open(
                        "tests/generated_plc_code.txt", "w", encoding="utf-8"
                    ) as f:
                        f.write(generated_code)
                    print("\n💾 代码已保存到 generated_plc_code.txt")

                    if generator.unmapped_conditions:
                        with open(
                            "database_mapping_suggestions.sql", "w", encoding="utf-8"
                        ) as f:
                            f.write(generator.get_condition_mapping_template())
                        print(
                            "💾 数据库映射建议已保存到 database_mapping_suggestions.sql"
                        )
                    else:
                        print("✅ 所有条件映射完整，无需补充!")
                else:
                    print("❌ 数据加载失败，请检查表名和数据结构")

        except Exception as e:
            print(f"❌ 程序执行出错: {e}")
        finally:
            generator.disconnect_database()

        return generator

    # 从数据库加载TP数据并生成代码
    def test_database_tp():
        print("\n🔧 从数据库加载TP流程数据...")

        # 使用数据库配置
        generator = PLCCodeGenerator(db_config)

        try:
            # 连接数据库并加载数据
            if generator.connect_database():
                # 加载TP步骤数据和命令映射数据
                success = generator.load_all_data_from_db(
                    "tp_control_logic", command_table
                )

                if success:
                    print(f"✅ 成功加载 {len(generator.step_data)} 个TP步骤")
                    print(f"✅ 成功加载 {len(generator.command_data)} 条命令映射数据")

                    # 详细打印每个步骤的数据
                    print("\n📋 详细步骤数据:")
                    print("=" * 120)
                    print(
                        f"{'ID':<4} {'StepID':<8} {'描述':<20} {'运动条件':<25} {'运动动作':<15} {'跳步条件':<30} {'跳步目标':<10} {'TP序列':<15} {'EM描述':<15}"
                    )
                    print("=" * 120)

                    for step in generator.step_data:
                        print(
                            f"{step.get('id', ''):<4} "
                            f"{step.get('step_id', ''):<8} "
                            f"{step.get('step_description', ''):<20} "
                            f"{step.get('motion_condition', ''):<25} "
                            f"{step.get('motion_action', ''):<15} "
                            f"{step.get('skip_condition', ''):<30} "
                            f"{step.get('skip_step', ''):<10} "
                            f"{step.get('tp_sequence', ''):<15} "
                            f"{step.get('em_description_cn', ''):<15}"
                        )

                    print("=" * 120)

                    # 打印条件映射信息
                    generator.print_condition_mappings_info()

                    tp_groups = generator.group_steps_by_tp()
                    print(f"\n📊 发现 {len(tp_groups)} 个TP流程:")
                    for tp_seq, steps in tp_groups.items():
                        em_desc = steps[0]["em_description_cn"] if steps else ""
                        print(f"  {tp_seq}: {em_desc} ({len(steps)} 个步骤)")

                    print("\n🔧 开始生成PLC代码...")
                    generated_code = generator.generate_all_code()

                    print("\n" + "=" * 80)
                    print("📄 生成的PLC代码:")
                    print("=" * 80)
                    print(generated_code)

                    if generator.unmapped_conditions:
                        print("\n" + "🔧 未映射的条件:")
                        print("=" * 60)
                        for condition in sorted(generator.unmapped_conditions):
                            print(f"  - {condition}")

                        print("\n" + "🔧 数据库条件映射补充建议:")
                        print("=" * 60)
                        print(generator.get_condition_mapping_template())

                    with open(
                        "tests/generated_plc_code_db.txt", "w", encoding="utf-8"
                    ) as f:
                        f.write(generated_code)
                    print("\n💾 代码已保存到 generated_plc_code_db.txt")

                    if generator.unmapped_conditions:
                        with open(
                            "database_mapping_suggestions.sql", "w", encoding="utf-8"
                        ) as f:
                            f.write(generator.get_condition_mapping_template())
                        print(
                            "💾 数据库映射建议已保存到 database_mapping_suggestions.sql"
                        )
                    else:
                        print("✅ 所有条件映射完整，无需补充!")
                else:
                    print("❌ 数据加载失败")
            else:
                print("❌ 数据库连接失败")

        except Exception as e:
            print(f"❌ 程序执行出错: {e}")
            import traceback

            traceback.print_exc()
        finally:
            generator.disconnect_database()


if __name__ == "__main__":
    # 数据库配置
    db_config = {
        "host": "*************",  # 数据库服务器地址
        "port": 3306,  # 端口号
        "user": "root",  # 用户名
        "password": "leadchina",  # 密码
        "database": "IO_TEST",  # 数据库名
        "charset": "utf8mb4",  # 字符集
    }

    command_table = "actuator_variable_map"

    try:
        # 运行数据库TP测试
        test_database_tp()
        print("\n✅ 数据库TP测试完成!")
    except Exception as e:
        print(f"❌ 数据库TP测试失败: {e}")
        import traceback

        traceback.print_exc()
